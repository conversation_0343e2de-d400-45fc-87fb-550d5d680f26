/**
 * API Specification Validator
 *
 * This script validates the generated OpenAPI 3.0 specification against
 * OpenAPI standards and checks for common issues.
 *
 * Usage:
 *   npx ts-node scripts/validate-api-spec.ts
 *
 * Validates:
 *   - OpenAPI 3.0 compliance
 *   - Schema definitions
 *   - Path operations
 *   - Security schemes
 *   - Response definitions
 */

import { existsSync } from 'fs';
import { join } from 'path';
import SwaggerParser from '@apidevtools/swagger-parser';

// Type definitions for OpenAPI specification
interface OpenAPISpec {
  openapi: string;
  info: {
    title: string;
    version: string;
    description?: string;
  };
  paths: Record<string, PathItem>;
  components?: {
    schemas?: Record<string, SchemaObject>;
    securitySchemes?: Record<string, SecurityScheme>;
  };
}

interface PathItem {
  [method: string]: Operation;
}

interface Operation {
  summary?: string;
  description?: string;
  security?: SecurityRequirement[];
  responses?: Record<string, ResponseObject>;
  parameters?: ParameterObject[];
}

interface SecurityRequirement {
  [name: string]: string[];
}

interface ResponseObject {
  description: string;
  content?: Record<string, MediaTypeObject>;
}

interface MediaTypeObject {
  schema?: SchemaObject;
  examples?: Record<string, unknown>;
}

interface SchemaObject {
  type?: string;
  properties?: Record<string, SchemaObject>;
  required?: string[];
}

interface SecurityScheme {
  type: string;
  scheme?: string;
  name?: string;
  in?: string;
}

interface ParameterObject {
  name: string;
  in: string;
  required?: boolean;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  summary: {
    endpoints: number;
    schemas: number;
    securitySchemes: number;
    operationsWithAuth: number;
    operationsWithExamples: number;
  };
}

async function validateApiSpecification(): Promise<ValidationResult> {
  console.log('🔍 Starting API specification validation...');

  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    summary: {
      endpoints: 0,
      schemas: 0,
      securitySchemes: 0,
      operationsWithAuth: 0,
      operationsWithExamples: 0,
    },
  };

  try {
    // Check if specification files exist
    const jsonPath = join(process.cwd(), 'docs', 'api', 'api-spec.json');
    const yamlPath = join(process.cwd(), 'docs', 'api', 'api-spec.yaml');

    if (!existsSync(jsonPath)) {
      result.errors.push(`JSON specification not found: ${jsonPath}`);
      result.isValid = false;
    }

    if (!existsSync(yamlPath)) {
      result.errors.push(`YAML specification not found: ${yamlPath}`);
      result.isValid = false;
    }

    if (!result.isValid) {
      return result;
    }

    // Parse and validate the OpenAPI specification
    console.log('📋 Parsing OpenAPI specification...');
    const api = (await SwaggerParser.validate(jsonPath)) as OpenAPISpec;
    console.log('✅ OpenAPI specification is valid!');

    // Generate summary statistics
    result.summary.endpoints = Object.keys(api.paths || {}).length;
    result.summary.schemas = Object.keys(api.components?.schemas || {}).length;
    result.summary.securitySchemes = Object.keys(
      api.components?.securitySchemes || {},
    ).length;

    // Validate paths and operations
    console.log('🔍 Validating paths and operations...');
    for (const [path, pathItem] of Object.entries(api.paths || {})) {
      if (!pathItem || typeof pathItem !== 'object') continue;

      for (const [method, operationData] of Object.entries(pathItem)) {
        if (
          !operationData ||
          typeof operationData !== 'object' ||
          method.startsWith('x-')
        )
          continue;

        const operation = operationData;

        // Check for security requirements
        if (operation.security && operation.security.length > 0) {
          result.summary.operationsWithAuth++;
        } else {
          result.warnings.push(
            `Operation ${method.toUpperCase()} ${path} has no security requirements`,
          );
        }

        // Check for examples in responses
        let hasExamples = false;
        if (operation.responses) {
          for (const [, response] of Object.entries(operation.responses)) {
            if (
              response &&
              typeof response === 'object' &&
              'content' in response
            ) {
              const content = response.content;
              if (content && typeof content === 'object') {
                for (const mediaType of Object.values(content)) {
                  if (
                    mediaType &&
                    typeof mediaType === 'object' &&
                    'examples' in mediaType
                  ) {
                    hasExamples = true;
                    break;
                  }
                }
              }
            }
            if (hasExamples) break;
          }
        }

        if (hasExamples) {
          result.summary.operationsWithExamples++;
        }

        // Check for required fields
        if (!operation.summary) {
          result.warnings.push(
            `Operation ${method.toUpperCase()} ${path} missing summary`,
          );
        }

        if (!operation.description) {
          result.warnings.push(
            `Operation ${method.toUpperCase()} ${path} missing description`,
          );
        }

        if (
          !operation.responses ||
          Object.keys(operation.responses).length === 0
        ) {
          result.errors.push(
            `Operation ${method.toUpperCase()} ${path} has no response definitions`,
          );
          result.isValid = false;
        }
      }
    }

    // Validate security schemes
    console.log('🔒 Validating security schemes...');
    const securitySchemes = api.components?.securitySchemes || {};

    if (Object.keys(securitySchemes).length === 0) {
      result.warnings.push('No security schemes defined');
    }

    for (const [name, schemeData] of Object.entries(securitySchemes)) {
      if (!schemeData || typeof schemeData !== 'object') continue;

      const scheme = schemeData;

      if (!scheme.type) {
        result.errors.push(`Security scheme '${name}' missing type`);
        result.isValid = false;
      }

      if (scheme.type === 'http' && !scheme.scheme) {
        result.errors.push(`HTTP security scheme '${name}' missing scheme`);
        result.isValid = false;
      }
    }

    // Validate schemas
    console.log('📝 Validating schemas...');
    const schemas = api.components?.schemas || {};

    for (const [name, schemaData] of Object.entries(schemas)) {
      if (!schemaData || typeof schemaData !== 'object') continue;

      const schema = schemaData;

      // Check for required properties in object schemas
      if (schema.type === 'object' && schema.properties) {
        const hasRequired =
          schema.required &&
          Array.isArray(schema.required) &&
          schema.required.length > 0;
        if (!hasRequired) {
          result.warnings.push(`Schema '${name}' has no required properties`);
        }
      }
    }

    console.log('\n📊 Validation Summary:');
    console.log(`   Endpoints: ${result.summary.endpoints}`);
    console.log(`   Schemas: ${result.summary.schemas}`);
    console.log(`   Security Schemes: ${result.summary.securitySchemes}`);
    console.log(
      `   Operations with Auth: ${result.summary.operationsWithAuth}`,
    );
    console.log(
      `   Operations with Examples: ${result.summary.operationsWithExamples}`,
    );
    console.log(`   Errors: ${result.errors.length}`);
    console.log(`   Warnings: ${result.warnings.length}`);

    if (result.errors.length > 0) {
      console.log('\n❌ Validation Errors:');
      result.errors.forEach((error) => console.log(`   - ${error}`));
      result.isValid = false;
    }

    if (result.warnings.length > 0) {
      console.log('\n⚠️  Validation Warnings:');
      result.warnings.forEach((warning) => console.log(`   - ${warning}`));
    }

    if (result.isValid) {
      console.log('\n🎉 API specification validation completed successfully!');
    } else {
      console.log('\n💥 API specification validation failed!');
    }
  } catch (error) {
    console.error('❌ Error validating API specification:', error);
    result.errors.push(
      `Validation error: ${error instanceof Error ? error.message : String(error)}`,
    );
    result.isValid = false;
  }

  return result;
}

// Run the validator
if (require.main === module) {
  validateApiSpecification()
    .then((result) => {
      process.exit(result.isValid ? 0 : 1);
    })
    .catch((error) => {
      console.error('Fatal error:', error);
      process.exit(1);
    });
}

export { validateApiSpecification };
