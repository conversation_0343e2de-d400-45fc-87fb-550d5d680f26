/**
 * API Specification Generator
 *
 * This script generates OpenAPI 3.0 specification files in JSON and YAML formats
 * from the NestJS application with Swagger decorators.
 *
 * Usage:
 *   npx ts-node scripts/generate-api-spec.ts
 *
 * Output:
 *   - api-spec.json: OpenAPI specification in JSON format
 *   - api-spec.yaml: OpenAPI specification in YAML format
 */

import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';
import * as yaml from 'js-yaml';
import { AppModule } from '../src/app.module';
import { ValidationPipe } from '../src/pipes/validation.pipe';
import { GlobalExceptionFilter } from '../src/filters/global-exception.filter';
import { LoggingInterceptor } from '../src/interceptors/logging.interceptor';
import {
  ErrorResponseSchema,
  ValidationErrorDetailSchema,
  ValidationErrorResponseSchema,
  AuthenticationErrorResponseSchema,
  NotFoundErrorResponseSchema,
  FileTooLargeErrorResponseSchema,
  InternalServerErrorResponseSchema,
} from '../src/schemas';

async function generateApiSpecification() {
  console.log('🚀 Starting API specification generation...');

  try {
    // Create NestJS application
    const app = await NestFactory.create(AppModule, {
      logger: false, // Disable logging during generation
    });

    // Apply the same configuration as main.ts
    app.useGlobalPipes(new ValidationPipe());
    app.useGlobalFilters(new GlobalExceptionFilter());
    app.useGlobalInterceptors(new LoggingInterceptor());

    // Configure Swagger/OpenAPI documentation (same as main.ts)
    const config = new DocumentBuilder()
      .setTitle('Teaching Material System (TMS) REST API')
      .setDescription(
        'A centralized service for storing, retrieving, updating, and deleting F2F paperless marking worked solution quiz materials',
      )
      .setVersion('0.0.1')
      .addBasicAuth(
        {
          type: 'http',
          scheme: 'basic',
          description: 'Basic Authentication using username and password',
        },
        'basic',
      )
      .addApiKey(
        {
          type: 'apiKey',
          name: 'X-Correlation-ID',
          in: 'header',
          description: 'Correlation ID for request tracing (UUID format)',
        },
        'correlation-id',
      )
      .build();

    const document = SwaggerModule.createDocument(app, config, {
      extraModels: [
        // Include error response schemas for documentation
        ErrorResponseSchema,
        ValidationErrorDetailSchema,
        ValidationErrorResponseSchema,
        AuthenticationErrorResponseSchema,
        NotFoundErrorResponseSchema,
        FileTooLargeErrorResponseSchema,
        InternalServerErrorResponseSchema,
      ],
    });

    // Create output directory if it doesn't exist
    const outputDir = join(process.cwd(), 'docs', 'api');
    mkdirSync(outputDir, { recursive: true });

    // Generate JSON specification
    const jsonPath = join(outputDir, 'api-spec.json');
    writeFileSync(jsonPath, JSON.stringify(document, null, 2));
    console.log(`✅ JSON specification generated: ${jsonPath}`);

    // Generate YAML specification
    const yamlContent = (
      yaml as { dump: (obj: unknown, options?: unknown) => string }
    ).dump(document, {
      indent: 2,
      lineWidth: 120,
      noRefs: true,
    });
    const yamlPath = join(outputDir, 'api-spec.yaml');
    writeFileSync(yamlPath, yamlContent);
    console.log(`✅ YAML specification generated: ${yamlPath}`);

    // Generate summary information
    const summary = {
      title: document.info.title,
      version: document.info.version,
      description: document.info.description,
      openApiVersion: document.openapi,
      generatedAt: new Date().toISOString(),
      endpoints: Object.keys(document.paths || {}).length,
      schemas: Object.keys(document.components?.schemas || {}).length,
      securitySchemes: Object.keys(document.components?.securitySchemes || {})
        .length,
    };

    const summaryPath = join(outputDir, 'api-summary.json');
    writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
    console.log(`✅ API summary generated: ${summaryPath}`);

    console.log('\n📊 API Specification Summary:');
    console.log(`   Title: ${summary.title}`);
    console.log(`   Version: ${summary.version}`);
    console.log(`   OpenAPI Version: ${summary.openApiVersion}`);
    console.log(`   Endpoints: ${summary.endpoints}`);
    console.log(`   Schemas: ${summary.schemas}`);
    console.log(`   Security Schemes: ${summary.securitySchemes}`);
    console.log(`   Generated: ${summary.generatedAt}`);

    await app.close();
    console.log('\n🎉 API specification generation completed successfully!');
  } catch (error) {
    console.error('❌ Error generating API specification:', error);
    process.exit(1);
  }
}

// Run the generator
if (require.main === module) {
  void generateApiSpecification();
}

export { generateApiSpecification };
