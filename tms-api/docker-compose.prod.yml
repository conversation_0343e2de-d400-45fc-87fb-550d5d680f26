# Production Docker Compose Configuration for TMS REST API
# SECURITY: This configuration is hardened for production deployment
# - No admin interfaces exposed
# - Uses Docker secrets for sensitive credentials
# - Internal networks with no exposed ports except reverse proxy
# - SSL enabled for MinIO

version: '3.8'

services:
  api:
    container_name: tms-api-prod-container
    image: tms-api-prod-container:latest
    build:
      context: .
      target: production
    networks:
      - tms-internal
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME_FILE=/run/secrets/db_username
      - DB_PASSWORD_FILE=/run/secrets/db_password
      - DB_DATABASE_FILE=/run/secrets/db_database
      - DB_SYNC=false
      - DB_LOGGING=false
      - MINIO_ENDPOINT=minio
      - MINIO_PORT=9000
      - MINIO_USE_SSL=true
      - MINIO_ACCESS_KEY_FILE=/run/secrets/minio_access_key
      - MINIO_SECRET_KEY_FILE=/run/secrets/minio_secret_key
      - MINIO_DEFAULT_BUCKET_FILE=/run/secrets/minio_bucket
      - AUTH_USERNAME_FILE=/run/secrets/auth_username
      - AUTH_PASSWORD_FILE=/run/secrets/auth_password
    secrets:
      - db_username
      - db_password
      - db_database
      - minio_access_key
      - minio_secret_key
      - minio_bucket
      - auth_username
      - auth_password
    depends_on:
      - postgres
      - minio
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    container_name: tms-postgres-prod-container
    image: postgres:16
    networks:
      - tms-internal
    environment:
      - POSTGRES_USER_FILE=/run/secrets/db_username
      - POSTGRES_PASSWORD_FILE=/run/secrets/db_password
      - POSTGRES_DB_FILE=/run/secrets/db_database
    secrets:
      - db_username
      - db_password
      - db_database
    volumes:
      - tms-postgres-prod-volume:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $$(cat /run/secrets/db_username) -d $$(cat /run/secrets/db_database)"]
      interval: 30s
      timeout: 10s
      retries: 3

  minio:
    container_name: tms-minio-prod-container
    image: minio/minio:latest
    networks:
      - tms-internal
    environment:
      - MINIO_ROOT_USER_FILE=/run/secrets/minio_access_key
      - MINIO_ROOT_PASSWORD_FILE=/run/secrets/minio_secret_key
    secrets:
      - minio_access_key
      - minio_secret_key
    volumes:
      - tms-minio-prod-volume:/data
      - ./ssl/minio:/root/.minio/certs
    command: server /data --console-address ":9001"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "https://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Reverse proxy for HTTPS termination (nginx)
  reverse-proxy:
    container_name: tms-reverse-proxy-prod-container
    image: nginx:alpine
    ports:
      - "443:443"
      - "80:80"
    networks:
      - tms-internal
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl/nginx:/etc/nginx/ssl:ro
    depends_on:
      - api
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  tms-internal:
    driver: bridge
    internal: false  # Set to true for complete isolation

volumes:
  tms-postgres-prod-volume:
    driver: local
  tms-minio-prod-volume:
    driver: local

secrets:
  db_username:
    file: ./secrets/db_username.txt
  db_password:
    file: ./secrets/db_password.txt
  db_database:
    file: ./secrets/db_database.txt
  minio_access_key:
    file: ./secrets/minio_access_key.txt
  minio_secret_key:
    file: ./secrets/minio_secret_key.txt
  minio_bucket:
    file: ./secrets/minio_bucket.txt
  auth_username:
    file: ./secrets/auth_username.txt
  auth_password:
    file: ./secrets/auth_password.txt

# SECURITY NOTES:
# 1. No pgAdmin service - removed for production security
# 2. All credentials use Docker secrets
# 3. Internal network isolates services
# 4. Only reverse proxy exposes ports
# 5. SSL enabled for MinIO
# 6. Health checks for all services
# 7. Restart policies for high availability
