/**
 * Authentication Documentation Tests
 *
 * These tests verify that authentication and security documentation is properly
 * configured in the Swagger/OpenAPI specification for Microstep 2.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import request from 'supertest';
import { ValidationPipe } from './pipes/validation.pipe';
import { GlobalExceptionFilter } from './filters/global-exception.filter';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { QuizController } from './quiz/quiz.controller';
import { QuizService } from './quiz/quiz.service';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ThrottlerModule } from '@nestjs/throttler';
import { AuthModule } from './auth/auth.module';
import {
  ErrorResponseSchema,
  ValidationErrorDetailSchema,
  ValidationErrorResponseSchema,
  AuthenticationErrorResponseSchema,
  NotFoundErrorResponseSchema,
  FileTooLargeErrorResponseSchema,
  InternalServerErrorResponseSchema,
} from './schemas';

describe('Authentication Documentation', () => {
  let app: INestApplication;
  let module: TestingModule;

  beforeAll(async () => {
    // Create a minimal test module without database dependencies
    const mockQuizService = {
      getQuizWorkedSolutions: jest.fn(),
      uploadQuizWorkedSolutions: jest.fn(),
      updateQuiz: jest.fn(),
      deleteQuiz: jest.fn(),
    };

    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env',
        }),
        ThrottlerModule.forRoot([
          {
            name: 'default',
            ttl: 60000,
            limit: 1000,
          },
        ]),
        AuthModule,
      ],
      controllers: [AppController, QuizController],
      providers: [
        AppService,
        {
          provide: QuizService,
          useValue: mockQuizService,
        },
      ],
    }).compile();

    app = module.createNestApplication();

    // Apply the same configuration as main.ts
    app.useGlobalPipes(new ValidationPipe());
    app.useGlobalFilters(new GlobalExceptionFilter());
    app.useGlobalInterceptors(new LoggingInterceptor());

    // Configure Swagger/OpenAPI documentation (same as main.ts)
    const config = new DocumentBuilder()
      .setTitle('Teaching Material System (TMS) REST API')
      .setDescription(
        'A centralized service for storing, retrieving, updating, and deleting F2F paperless marking worked solution quiz materials',
      )
      .setVersion('0.0.1')
      .addBasicAuth(
        {
          type: 'http',
          scheme: 'basic',
          description: 'Basic Authentication using username and password',
        },
        'basic',
      )
      .addApiKey(
        {
          type: 'apiKey',
          name: 'X-Correlation-ID',
          in: 'header',
          description: 'Correlation ID for request tracing (UUID format)',
        },
        'correlation-id',
      )
      .build();

    const document = SwaggerModule.createDocument(app, config, {
      extraModels: [
        ErrorResponseSchema,
        ValidationErrorDetailSchema,
        ValidationErrorResponseSchema,
        AuthenticationErrorResponseSchema,
        NotFoundErrorResponseSchema,
        FileTooLargeErrorResponseSchema,
        InternalServerErrorResponseSchema,
      ],
    });
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
      },
    });

    await app.init();
  });

  afterAll(async () => {
    try {
      if (app) {
        await app.close();
      }
      if (module) {
        await module.close();
      }
    } catch (error) {
      console.error('Error during test cleanup:', error);
    }
  });

  describe('Security Schemes Documentation', () => {
    it('should document Basic Auth security scheme correctly', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/docs-json')
        .expect(200);

      const openApiSpec = response.body;

      // Verify Basic Auth security scheme
      expect(openApiSpec.components.securitySchemes).toHaveProperty('basic');
      const basicAuth = openApiSpec.components.securitySchemes.basic;

      expect(basicAuth).toMatchObject({
        type: 'http',
        scheme: 'basic',
        description: 'Basic Authentication using username and password',
      });
    });

    it('should document X-Correlation-ID header requirement correctly', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/docs-json')
        .expect(200);

      const openApiSpec = response.body;

      // Verify X-Correlation-ID security scheme
      expect(openApiSpec.components.securitySchemes).toHaveProperty(
        'correlation-id',
      );
      const correlationId =
        openApiSpec.components.securitySchemes['correlation-id'];

      expect(correlationId).toMatchObject({
        type: 'apiKey',
        name: 'X-Correlation-ID',
        in: 'header',
        description: 'Correlation ID for request tracing (UUID format)',
      });
    });
  });

  describe('Protected Endpoints Security Documentation', () => {
    it('should document security requirements for quiz endpoints', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/docs-json')
        .expect(200);

      const openApiSpec = response.body;

      // Check quiz endpoints have security requirements
      const quizPath = '/quiz/f2f/paperless-marking-worked-solutions';
      expect(openApiSpec.paths).toHaveProperty(quizPath);

      const quizEndpoint = openApiSpec.paths[quizPath];

      // GET endpoint should have security
      expect(quizEndpoint.get).toHaveProperty('security');
      expect(Array.isArray(quizEndpoint.get.security)).toBe(true);

      // POST endpoint should have security
      expect(quizEndpoint.post).toHaveProperty('security');
      expect(Array.isArray(quizEndpoint.post.security)).toBe(true);
    });

    it('should document security requirements for protected app endpoint', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/docs-json')
        .expect(200);

      const openApiSpec = response.body;

      // Check protected endpoint has security requirements
      expect(openApiSpec.paths).toHaveProperty('/protected');
      const protectedEndpoint = openApiSpec.paths['/protected'];

      expect(protectedEndpoint.get).toHaveProperty('security');
      expect(Array.isArray(protectedEndpoint.get.security)).toBe(true);
    });

    it('should document X-Correlation-ID header for all endpoints', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/docs-json')
        .expect(200);

      const openApiSpec = response.body;

      // Check that endpoints have X-Correlation-ID parameter documentation
      const paths = openApiSpec.paths;

      // Check root endpoint
      expect(paths['/']).toBeDefined();
      expect(paths['/'].get.parameters).toBeDefined();

      // Check protected endpoint
      expect(paths['/protected']).toBeDefined();
      expect(paths['/protected'].get.parameters).toBeDefined();

      // Check correlation test endpoint
      expect(paths['/correlation-test']).toBeDefined();
      expect(paths['/correlation-test'].get.parameters).toBeDefined();
    });
  });

  describe('Error Response Schemas Documentation', () => {
    it('should include all error response schemas in components', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/docs-json')
        .expect(200);

      const openApiSpec = response.body;
      const schemas = openApiSpec.components.schemas;

      // Verify all error schemas are present
      expect(schemas).toHaveProperty('ErrorResponseSchema');
      expect(schemas).toHaveProperty('ValidationErrorDetailSchema');
      expect(schemas).toHaveProperty('ValidationErrorResponseSchema');
      expect(schemas).toHaveProperty('AuthenticationErrorResponseSchema');
      expect(schemas).toHaveProperty('NotFoundErrorResponseSchema');
      expect(schemas).toHaveProperty('FileTooLargeErrorResponseSchema');
      expect(schemas).toHaveProperty('InternalServerErrorResponseSchema');
    });

    it('should document error response structure correctly', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/docs-json')
        .expect(200);

      const openApiSpec = response.body;
      const errorSchema = openApiSpec.components.schemas.ErrorResponseSchema;

      // Verify error response structure matches Design Doc
      expect(errorSchema.properties).toHaveProperty('statusCode');
      expect(errorSchema.properties).toHaveProperty('message');
      expect(errorSchema.properties).toHaveProperty('details');
      expect(errorSchema.properties).toHaveProperty('correlationId');
      expect(errorSchema.properties).toHaveProperty('timestamp');
      expect(errorSchema.properties).toHaveProperty('path');

      // Verify required fields
      expect(errorSchema.required).toContain('statusCode');
      expect(errorSchema.required).toContain('message');
      expect(errorSchema.required).toContain('timestamp');
      expect(errorSchema.required).toContain('path');
    });

    it('should document authentication error responses correctly', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/docs-json')
        .expect(200);

      const openApiSpec = response.body;
      const authErrorSchema =
        openApiSpec.components.schemas.AuthenticationErrorResponseSchema;

      // Verify authentication error structure
      expect(authErrorSchema.properties.statusCode.example).toBe(401);
      expect(authErrorSchema.properties.message.example).toBe('Unauthorized');
      expect(authErrorSchema.properties).toHaveProperty('correlationId');
      expect(authErrorSchema.properties).toHaveProperty('timestamp');
      expect(authErrorSchema.properties).toHaveProperty('path');
    });
  });
});
