import { Controller, Get, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBasicAuth,
  ApiHeader,
} from '@nestjs/swagger';
import { AppService } from './app.service';
import { BasicAuthGuard } from './auth/guards/basic-auth.guard';
import { CorrelationId } from './decorators/correlation-id.decorator';

@ApiTags('General')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: 'Get hello message' })
  @ApiResponse({ status: 200, description: 'Returns hello message' })
  @ApiHeader({
    name: 'X-Correlation-ID',
    description: 'Correlation ID for request tracing (UUID format)',
    required: true,
    schema: {
      type: 'string',
      format: 'uuid',
      example: '123e4567-e89b-12d3-a456-************',
    },
  })
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('protected')
  @UseGuards(BasicAuthGuard)
  @ApiOperation({ summary: 'Protected endpoint requiring Basic Auth' })
  @ApiBasicAuth('basic')
  @ApiHeader({
    name: 'X-Correlation-ID',
    description: 'Correlation ID for request tracing (UUID format)',
    required: true,
    schema: {
      type: 'string',
      format: 'uuid',
      example: '123e4567-e89b-12d3-a456-************',
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully authenticated',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'This is a protected endpoint that requires Basic Auth!',
        },
        correlationId: {
          type: 'string',
          format: 'uuid',
          example: '123e4567-e89b-12d3-a456-************',
        },
        timestamp: {
          type: 'string',
          format: 'date-time',
          example: '2025-05-26T21:45:23.926Z',
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Unauthorized' },
        correlationId: {
          type: 'string',
          format: 'uuid',
          example: '123e4567-e89b-12d3-a456-************',
        },
        timestamp: {
          type: 'string',
          format: 'date-time',
          example: '2025-05-26T21:45:23.926Z',
        },
        path: { type: 'string', example: '/protected' },
      },
    },
  })
  getProtected(@CorrelationId() correlationId: string): object {
    return {
      message: 'This is a protected endpoint that requires Basic Auth!',
      correlationId,
      timestamp: new Date().toISOString(),
    };
  }

  @Get('correlation-test')
  @ApiOperation({ summary: 'Test correlation ID functionality' })
  @ApiHeader({
    name: 'X-Correlation-ID',
    description: 'Correlation ID for request tracing (UUID format)',
    required: true,
    schema: {
      type: 'string',
      format: 'uuid',
      example: '123e4567-e89b-12d3-a456-************',
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Returns correlation ID test response',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Correlation ID test endpoint' },
        correlationId: {
          type: 'string',
          format: 'uuid',
          example: '123e4567-e89b-12d3-a456-************',
        },
        timestamp: {
          type: 'string',
          format: 'date-time',
          example: '2025-05-26T21:45:23.926Z',
        },
      },
    },
  })
  getCorrelationTest(@CorrelationId() correlationId: string): object {
    return {
      message: 'Correlation ID test endpoint',
      correlationId,
      timestamp: new Date().toISOString(),
    };
  }
}
