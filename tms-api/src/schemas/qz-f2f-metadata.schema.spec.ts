/**
 * QzF2f Metadata Schema Tests
 *
 * Comprehensive tests for the QzF2fMetadataSchema Zod validation.
 * These tests verify all validation rules, edge cases, and error scenarios.
 */

import {
  QzF2fMetadataSchema,
  QzF2fQuestionSchema,
  type QzF2fMetadata,
  type QzF2fQuestion,
} from './qz-f2f-metadata.schema';
import { ZodError } from 'zod';

describe('QzF2fQuestionSchema', () => {
  describe('Valid Data', () => {
    it('should validate a complete valid question object', () => {
      const validQuestion = {
        questionId: '1137',
        questionNumber: '1',
        smilFile: 'Q1137.smil',
        marksJson: '[{"index": "0", "mark": 0}]',
      };

      const result = QzF2fQuestionSchema.parse(validQuestion);

      expect(result).toEqual(validQuestion);
      expect(result.questionId).toBe('1137');
      expect(result.questionNumber).toBe('1');
      expect(result.smilFile).toBe('Q1137.smil');
      expect(result.marksJson).toBe('[{"index": "0", "mark": 0}]');
    });

    it('should validate with only required fields', () => {
      const minimalQuestion = {
        questionId: '2013',
        questionNumber: '2',
      };

      const result = QzF2fQuestionSchema.parse(minimalQuestion);

      expect(result.questionId).toBe('2013');
      expect(result.questionNumber).toBe('2');
      expect(result.smilFile).toBeUndefined();
      expect(result.marksJson).toBeUndefined();
    });

    it('should validate complex question numbering patterns', () => {
      const complexNumbers = ['1(a)', '2(b)(i)', '3(c)(ii)', '4(d)(iii)', '5'];

      complexNumbers.forEach((questionNumber, index) => {
        const question = {
          questionId: `${1000 + index}`,
          questionNumber,
          smilFile: `Q${1000 + index}.smil`,
          marksJson: '[{"index": "0", "mark": 0}]',
        };

        const result = QzF2fQuestionSchema.parse(question);
        expect(result.questionNumber).toBe(questionNumber);
      });
    });

    it('should validate different marking scheme formats', () => {
      const markingSchemes = [
        '[{"index": "0", "mark": 0}]',
        '[{"index": "0", "mark": 0}, {"index": "1", "mark": 1}]',
        '[{"index": "0", "mark": 0}, {"index": "1", "mark": 1}, {"index": "2", "mark": 2}]',
        '[]', // Empty marking scheme
        '{"total": 5, "breakdown": [1, 2, 2]}', // Different format
      ];

      markingSchemes.forEach((marksJson, index) => {
        const question = {
          questionId: `${2000 + index}`,
          questionNumber: `${index + 1}`,
          smilFile: `Q${2000 + index}.smil`,
          marksJson,
        };

        const result = QzF2fQuestionSchema.parse(question);
        expect(result.marksJson).toBe(marksJson);
      });
    });

    it('should validate boundary conditions for question IDs', () => {
      const boundaryIds = ['1', '9999', '0001', '1137', 'Q123', 'ABC123'];

      boundaryIds.forEach((questionId, index) => {
        const question = {
          questionId,
          questionNumber: `${index + 1}`,
        };

        const result = QzF2fQuestionSchema.parse(question);
        expect(result.questionId).toBe(questionId);
      });
    });

    it('should validate maximum length strings', () => {
      const maxLengthQuestion = {
        questionId: 'A'.repeat(50), // Maximum allowed length
        questionNumber: 'B'.repeat(50), // Maximum allowed length
        smilFile: 'C'.repeat(255), // Maximum allowed length
        marksJson: 'D'.repeat(5000), // Maximum allowed length
      };

      const result = QzF2fQuestionSchema.parse(maxLengthQuestion);
      expect(result.questionId).toBe('A'.repeat(50));
      expect(result.questionNumber).toBe('B'.repeat(50));
      expect(result.smilFile).toBe('C'.repeat(255));
      expect(result.marksJson).toBe('D'.repeat(5000));
    });
  });

  describe('Invalid Data', () => {
    it('should reject empty questionId', () => {
      const invalidQuestion = {
        questionId: '',
        questionNumber: '1',
      };

      expect(() => QzF2fQuestionSchema.parse(invalidQuestion)).toThrow(
        ZodError,
      );

      try {
        QzF2fQuestionSchema.parse(invalidQuestion);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['questionId']);
        expect(zodError.errors[0].message).toBe(
          'Question ID is required and cannot be empty',
        );
      }
    });

    it('should reject empty questionNumber', () => {
      const invalidQuestion = {
        questionId: '1137',
        questionNumber: '',
      };

      expect(() => QzF2fQuestionSchema.parse(invalidQuestion)).toThrow(
        ZodError,
      );

      try {
        QzF2fQuestionSchema.parse(invalidQuestion);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['questionNumber']);
        expect(zodError.errors[0].message).toBe(
          'Question number is required and cannot be empty',
        );
      }
    });

    it('should reject questionId longer than 50 characters', () => {
      const invalidQuestion = {
        questionId: 'A'.repeat(51),
        questionNumber: '1',
      };

      expect(() => QzF2fQuestionSchema.parse(invalidQuestion)).toThrow(
        ZodError,
      );

      try {
        QzF2fQuestionSchema.parse(invalidQuestion);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['questionId']);
        expect(zodError.errors[0].message).toBe(
          'Question ID must be at most 50 characters',
        );
      }
    });

    it('should reject smilFile longer than 255 characters', () => {
      const invalidQuestion = {
        questionId: '1137',
        questionNumber: '1',
        smilFile: 'A'.repeat(256),
      };

      expect(() => QzF2fQuestionSchema.parse(invalidQuestion)).toThrow(
        ZodError,
      );

      try {
        QzF2fQuestionSchema.parse(invalidQuestion);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['smilFile']);
        expect(zodError.errors[0].message).toBe(
          'SMIL file must be at most 255 characters',
        );
      }
    });

    it('should reject marksJson longer than 5000 characters', () => {
      const invalidQuestion = {
        questionId: '1137',
        questionNumber: '1',
        marksJson: 'A'.repeat(5001),
      };

      expect(() => QzF2fQuestionSchema.parse(invalidQuestion)).toThrow(
        ZodError,
      );

      try {
        QzF2fQuestionSchema.parse(invalidQuestion);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['marksJson']);
        expect(zodError.errors[0].message).toBe(
          'Marks JSON must be at most 5000 characters',
        );
      }
    });

    it('should reject missing required fields', () => {
      const invalidQuestion = {
        smilFile: 'Q1137.smil',
        marksJson: '[{"index": "0", "mark": 0}]',
      };

      expect(() => QzF2fQuestionSchema.parse(invalidQuestion)).toThrow(
        ZodError,
      );

      try {
        QzF2fQuestionSchema.parse(invalidQuestion);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors).toHaveLength(2); // questionId and questionNumber are required

        const errorPaths = zodError.errors.map((err) => err.path[0]);
        expect(errorPaths).toContain('questionId');
        expect(errorPaths).toContain('questionNumber');
      }
    });
  });
});

describe('QzF2fMetadataSchema', () => {
  describe('Valid Data', () => {
    it('should validate a valid array of questions', () => {
      const validMetadata = [
        {
          questionId: '1137',
          questionNumber: '1',
          smilFile: 'Q1137.smil',
          marksJson: '[{"index": "0", "mark": 0}]',
        },
        {
          questionId: '2013',
          questionNumber: '2',
          smilFile: 'Q2013.smil',
          marksJson: '[{"index": "0", "mark": 0}]',
        },
      ];

      const result = QzF2fMetadataSchema.parse(validMetadata);

      expect(result).toEqual(validMetadata);
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(2);
      expect(result[0].questionId).toBe('1137');
      expect(result[1].questionId).toBe('2013');
    });

    it('should validate a single question array', () => {
      const validMetadata = [
        {
          questionId: '1137',
          questionNumber: '1',
        },
      ];

      const result = QzF2fMetadataSchema.parse(validMetadata);

      expect(result).toEqual(validMetadata);
      expect(result).toHaveLength(1);
    });

    it('should validate a large array of questions', () => {
      const largeMetadata: any[] = [];
      for (let i = 1; i <= 20; i++) {
        largeMetadata.push({
          questionId: `${1000 + i}`,
          questionNumber: `${i}`,
          smilFile: `Q${1000 + i}.smil`,
          marksJson: '[{"index": "0", "mark": 0}]',
        });
      }

      const result = QzF2fMetadataSchema.parse(largeMetadata);

      expect(result).toEqual(largeMetadata);
      expect(result).toHaveLength(20);
      expect(result[0].questionId).toBe('1001');
      expect(result[19].questionId).toBe('1020');
    });

    it('should validate mixed question formats', () => {
      const mixedMetadata = [
        {
          questionId: '1137',
          questionNumber: '1',
          smilFile: 'Q1137.smil',
          marksJson: '[{"index": "0", "mark": 0}]',
        },
        {
          questionId: '1138',
          questionNumber: '2(a)',
          // No optional fields
        },
        {
          questionId: '2013',
          questionNumber: '2(b)',
          smilFile: 'Q2013.smil',
          // No marksJson
        },
      ];

      const result = QzF2fMetadataSchema.parse(mixedMetadata);

      expect(result).toEqual(mixedMetadata);
      expect(result).toHaveLength(3);
      expect(result[1].smilFile).toBeUndefined();
      expect(result[2].marksJson).toBeUndefined();
    });
  });

  describe('Invalid Data', () => {
    it('should reject empty array', () => {
      const invalidMetadata: unknown[] = [];

      expect(() => QzF2fMetadataSchema.parse(invalidMetadata)).toThrow(
        ZodError,
      );

      try {
        QzF2fMetadataSchema.parse(invalidMetadata);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].message).toBe(
          'QzF2f.json must contain at least one question',
        );
      }
    });

    it('should reject non-array input', () => {
      const invalidMetadata = {
        questionId: '1137',
        questionNumber: '1',
      };

      expect(() => QzF2fMetadataSchema.parse(invalidMetadata)).toThrow(
        ZodError,
      );

      try {
        QzF2fMetadataSchema.parse(invalidMetadata);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].message).toBe(
          'Expected array, received object',
        );
      }
    });

    it('should reject array with invalid question objects', () => {
      const invalidMetadata = [
        {
          questionId: '1137',
          questionNumber: '1',
        },
        {
          questionId: '', // Invalid: empty questionId
          questionNumber: '2',
        },
      ];

      expect(() => QzF2fMetadataSchema.parse(invalidMetadata)).toThrow(
        ZodError,
      );

      try {
        QzF2fMetadataSchema.parse(invalidMetadata);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual([1, 'questionId']);
        expect(zodError.errors[0].message).toBe(
          'Question ID is required and cannot be empty',
        );
      }
    });

    it('should reject array with missing required fields', () => {
      const invalidMetadata = [
        {
          questionId: '1137',
          // Missing questionNumber
        },
        {
          questionNumber: '2',
          // Missing questionId
        },
      ];

      expect(() => QzF2fMetadataSchema.parse(invalidMetadata)).toThrow(
        ZodError,
      );

      try {
        QzF2fMetadataSchema.parse(invalidMetadata);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors).toHaveLength(2);

        const errorPaths = zodError.errors.map((err) => err.path);
        expect(errorPaths).toContainEqual([0, 'questionNumber']);
        expect(errorPaths).toContainEqual([1, 'questionId']);
      }
    });
  });

  describe('TypeScript Type Inference', () => {
    it('should properly infer TypeScript types', () => {
      const validMetadata = [
        {
          questionId: '1137',
          questionNumber: '1',
          smilFile: 'Q1137.smil',
          marksJson: '[{"index": "0", "mark": 0}]',
        },
      ];

      const result: QzF2fMetadata = QzF2fMetadataSchema.parse(validMetadata);
      const question: QzF2fQuestion = result[0];

      // TypeScript should infer these types correctly
      expect(typeof question.questionId).toBe('string');
      expect(typeof question.questionNumber).toBe('string');
      expect(typeof question.smilFile).toBe('string');
      expect(typeof question.marksJson).toBe('string');
    });
  });
});
