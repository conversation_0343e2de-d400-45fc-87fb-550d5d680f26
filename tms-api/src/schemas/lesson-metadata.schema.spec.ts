/**
 * Lesson Metadata Schema Tests
 *
 * Comprehensive tests for the LessonMetadataSchema Zod validation.
 * These tests verify all validation rules, edge cases, and error scenarios.
 */

import {
  LessonMetadataSchema,
  type LessonMetadata,
} from './lesson-metadata.schema';
import { ZodError } from 'zod';

describe('LessonMetadataSchema', () => {
  describe('Valid Data', () => {
    it('should validate a complete valid lesson metadata object', () => {
      const validData = {
        grade: 9,
        subject: 'Math',
        course: '3U',
        classLevel: 'A1',
        color: 'R',
        topic: 'Trigonometry',
      };

      const result = LessonMetadataSchema.parse(validData);

      expect(result).toEqual(validData);
      expect(result.grade).toBe(9);
      expect(result.subject).toBe('Math');
      expect(result.course).toBe('3U');
      expect(result.classLevel).toBe('A1');
      expect(result.color).toBe('R');
      expect(result.topic).toBe('Trigonometry');
    });

    it('should validate with empty course field', () => {
      const validData = {
        grade: 10,
        subject: 'Science',
        course: '', // Empty course is allowed
        classLevel: 'B',
        color: 'Y',
        topic: 'Chemistry Basics',
      };

      const result = LessonMetadataSchema.parse(validData);
      expect(result).toEqual(validData);
      expect(result.course).toBe('');
    });

    it('should validate with minimum grade (1)', () => {
      const validData = {
        grade: 1,
        subject: 'English',
        course: '',
        classLevel: 'A',
        color: 'R',
        topic: 'Reading Basics',
      };

      const result = LessonMetadataSchema.parse(validData);
      expect(result.grade).toBe(1);
    });

    it('should validate with maximum grade (12)', () => {
      const validData = {
        grade: 12,
        subject: 'Math',
        course: '4U',
        classLevel: 'A1',
        color: 'Y',
        topic: 'Advanced Calculus',
      };

      const result = LessonMetadataSchema.parse(validData);
      expect(result.grade).toBe(12);
    });

    it('should validate with different class level variations', () => {
      const classLevels = ['A', 'A1', 'A2', 'A3', 'B', 'B1', 'C'];

      classLevels.forEach((classLevel) => {
        const validData = {
          grade: 11,
          subject: 'Math',
          course: '3U',
          classLevel,
          color: 'R',
          topic: 'Polynomial Functions',
        };

        const result = LessonMetadataSchema.parse(validData);
        expect(result.classLevel).toBe(classLevel);
      });
    });

    it('should validate with different color codes', () => {
      const colors = ['R', 'Y', 'G', 'B']; // Red, Yellow, Green, Blue

      colors.forEach((color) => {
        const validData = {
          grade: 9,
          subject: 'Math',
          course: '',
          classLevel: 'A1',
          color,
          topic: 'Algebra',
        };

        const result = LessonMetadataSchema.parse(validData);
        expect(result.color).toBe(color);
      });
    });

    it('should validate with long topic names', () => {
      const longTopic = 'A'.repeat(255); // Maximum allowed length

      const validData = {
        grade: 11,
        subject: 'Math',
        course: '3U',
        classLevel: 'A1',
        color: 'R',
        topic: longTopic,
      };

      const result = LessonMetadataSchema.parse(validData);
      expect(result.topic).toBe(longTopic);
    });
  });

  describe('Invalid Data - Grade Validation', () => {
    it('should reject grade below minimum (0)', () => {
      const invalidData = {
        grade: 0,
        subject: 'Math',
        course: '',
        classLevel: 'A1',
        color: 'R',
        topic: 'Test',
      };

      expect(() => LessonMetadataSchema.parse(invalidData)).toThrow(ZodError);

      try {
        LessonMetadataSchema.parse(invalidData);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['grade']);
        expect(zodError.errors[0].message).toBe('Grade must be at least 1');
      }
    });

    it('should reject grade above maximum (13)', () => {
      const invalidData = {
        grade: 13,
        subject: 'Math',
        course: '',
        classLevel: 'A1',
        color: 'R',
        topic: 'Test',
      };

      expect(() => LessonMetadataSchema.parse(invalidData)).toThrow(ZodError);

      try {
        LessonMetadataSchema.parse(invalidData);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['grade']);
        expect(zodError.errors[0].message).toBe('Grade must be at most 12');
      }
    });

    it('should reject non-integer grade', () => {
      const invalidData = {
        grade: 9.5,
        subject: 'Math',
        course: '',
        classLevel: 'A1',
        color: 'R',
        topic: 'Test',
      };

      expect(() => LessonMetadataSchema.parse(invalidData)).toThrow(ZodError);

      try {
        LessonMetadataSchema.parse(invalidData);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['grade']);
        expect(zodError.errors[0].message).toBe('Grade must be an integer');
      }
    });
  });

  describe('Invalid Data - String Field Validation', () => {
    it('should reject empty subject', () => {
      const invalidData = {
        grade: 9,
        subject: '',
        course: '',
        classLevel: 'A1',
        color: 'R',
        topic: 'Test',
      };

      expect(() => LessonMetadataSchema.parse(invalidData)).toThrow(ZodError);

      try {
        LessonMetadataSchema.parse(invalidData);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['subject']);
        expect(zodError.errors[0].message).toBe(
          'Subject is required and cannot be empty',
        );
      }
    });

    it('should reject empty class level', () => {
      const invalidData = {
        grade: 9,
        subject: 'Math',
        course: '',
        classLevel: '',
        color: 'R',
        topic: 'Test',
      };

      expect(() => LessonMetadataSchema.parse(invalidData)).toThrow(ZodError);

      try {
        LessonMetadataSchema.parse(invalidData);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['classLevel']);
        expect(zodError.errors[0].message).toBe(
          'Class level is required and cannot be empty',
        );
      }
    });

    it('should reject empty color', () => {
      const invalidData = {
        grade: 9,
        subject: 'Math',
        course: '',
        classLevel: 'A1',
        color: '',
        topic: 'Test',
      };

      expect(() => LessonMetadataSchema.parse(invalidData)).toThrow(ZodError);

      try {
        LessonMetadataSchema.parse(invalidData);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['color']);
        expect(zodError.errors[0].message).toBe(
          'Color is required and cannot be empty',
        );
      }
    });

    it('should reject empty topic', () => {
      const invalidData = {
        grade: 9,
        subject: 'Math',
        course: '',
        classLevel: 'A1',
        color: 'R',
        topic: '',
      };

      expect(() => LessonMetadataSchema.parse(invalidData)).toThrow(ZodError);

      try {
        LessonMetadataSchema.parse(invalidData);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['topic']);
        expect(zodError.errors[0].message).toBe(
          'Topic is required and cannot be empty',
        );
      }
    });
  });

  describe('Invalid Data - String Length Validation', () => {
    it('should reject subject longer than 100 characters', () => {
      const longSubject = 'A'.repeat(101);
      const invalidData = {
        grade: 9,
        subject: longSubject,
        course: '',
        classLevel: 'A1',
        color: 'R',
        topic: 'Test',
      };

      expect(() => LessonMetadataSchema.parse(invalidData)).toThrow(ZodError);

      try {
        LessonMetadataSchema.parse(invalidData);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['subject']);
        expect(zodError.errors[0].message).toBe(
          'Subject must be at most 100 characters',
        );
      }
    });

    it('should reject course longer than 100 characters', () => {
      const longCourse = 'A'.repeat(101);
      const invalidData = {
        grade: 9,
        subject: 'Math',
        course: longCourse,
        classLevel: 'A1',
        color: 'R',
        topic: 'Test',
      };

      expect(() => LessonMetadataSchema.parse(invalidData)).toThrow(ZodError);

      try {
        LessonMetadataSchema.parse(invalidData);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['course']);
        expect(zodError.errors[0].message).toBe(
          'Course must be at most 100 characters',
        );
      }
    });

    it('should reject topic longer than 255 characters', () => {
      const longTopic = 'A'.repeat(256);
      const invalidData = {
        grade: 9,
        subject: 'Math',
        course: '',
        classLevel: 'A1',
        color: 'R',
        topic: longTopic,
      };

      expect(() => LessonMetadataSchema.parse(invalidData)).toThrow(ZodError);

      try {
        LessonMetadataSchema.parse(invalidData);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['topic']);
        expect(zodError.errors[0].message).toBe(
          'Topic must be at most 255 characters',
        );
      }
    });
  });

  describe('Missing Required Fields', () => {
    it('should reject missing all fields', () => {
      const invalidData = {};

      expect(() => LessonMetadataSchema.parse(invalidData)).toThrow(ZodError);

      try {
        LessonMetadataSchema.parse(invalidData);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors).toHaveLength(6); // All 6 fields are required

        const errorPaths = zodError.errors.map((err) => err.path[0]);
        expect(errorPaths).toContain('grade');
        expect(errorPaths).toContain('subject');
        expect(errorPaths).toContain('course');
        expect(errorPaths).toContain('classLevel');
        expect(errorPaths).toContain('color');
        expect(errorPaths).toContain('topic');
      }
    });

    it('should reject missing grade field', () => {
      const invalidData = {
        subject: 'Math',
        course: '',
        classLevel: 'A1',
        color: 'R',
        topic: 'Test',
      };

      expect(() => LessonMetadataSchema.parse(invalidData)).toThrow(ZodError);

      try {
        LessonMetadataSchema.parse(invalidData);
      } catch (error) {
        expect(error).toBeInstanceOf(ZodError);
        const zodError = error as ZodError;
        expect(zodError.errors[0].path).toEqual(['grade']);
        expect(zodError.errors[0].message).toBe('Required');
      }
    });
  });

  describe('TypeScript Type Inference', () => {
    it('should properly infer TypeScript types', () => {
      const validData = {
        grade: 9,
        subject: 'Math',
        course: '3U',
        classLevel: 'A1',
        color: 'R',
        topic: 'Trigonometry',
      };

      const result: LessonMetadata = LessonMetadataSchema.parse(validData);

      // TypeScript should infer these types correctly
      expect(typeof result.grade).toBe('number');
      expect(typeof result.subject).toBe('string');
      expect(typeof result.course).toBe('string');
      expect(typeof result.classLevel).toBe('string');
      expect(typeof result.color).toBe('string');
      expect(typeof result.topic).toBe('string');
    });
  });
});
