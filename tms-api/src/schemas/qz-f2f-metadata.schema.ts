/**
 * Zod Schema for QzF2f.json Validation
 *
 * This schema validates the structure and content of QzF2f.json files
 * extracted from quiz ZIP uploads. It replaces the manual validation logic
 * with type-safe Zod validation.
 */

import { z } from 'zod';

/**
 * Zod schema for individual question objects in QzF2f.json
 *
 * Validates each question object based on:
 * - Current validation logic in quiz.service.ts
 * - DTO validation rules in InternalMetadataDto
 * - Training data patterns from test files
 */
export const QzF2fQuestionSchema = z.object({
  /**
   * Unique identifier for the question
   * Required field, must be a non-empty string with max length 50
   */
  questionId: z
    .string()
    .min(1, 'Question ID is required and cannot be empty')
    .max(50, 'Question ID must be at most 50 characters'),

  /**
   * Question number (can include sub-parts like "2(a)", "3(d)(i)")
   * Required field, must be a non-empty string with max length 50
   */
  questionNumber: z
    .string()
    .min(1, 'Question number is required and cannot be empty')
    .max(50, 'Question number must be at most 50 characters'),

  /**
   * SMIL file reference (if applicable)
   * Optional field, max length 255
   */
  smilFile: z
    .string()
    .max(255, 'SMIL file must be at most 255 characters')
    .optional(),

  /**
   * Marks/scoring information as JSON string
   * Optional field, max length 5000
   * Examples from training data: '[{"index": "0", "mark": 0}]'
   */
  marksJson: z
    .string()
    .max(5000, 'Marks JSON must be at most 5000 characters')
    .optional(),
});

/**
 * Zod schema for QzF2f.json validation
 *
 * QzF2f.json must contain an array of question objects
 *
 * @example
 * ```typescript
 * const metadata = QzF2fMetadataSchema.parse(jsonData);
 * // metadata is now type-safe and validated
 * ```
 */
export const QzF2fMetadataSchema = z
  .array(QzF2fQuestionSchema)
  .min(1, 'QzF2f.json must contain at least one question');

/**
 * TypeScript types inferred from the Zod schemas
 * These provide type safety when using the validated data
 */
export type QzF2fQuestion = z.infer<typeof QzF2fQuestionSchema>;
export type QzF2fMetadata = z.infer<typeof QzF2fMetadataSchema>;
