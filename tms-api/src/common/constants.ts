/**
 * Application Constants
 *
 * This file contains shared constants used throughout the application.
 * Centralizing constants helps maintain consistency and makes updates easier.
 */

// HTTP Headers
export const HEADERS = {
  CORRELATION_ID: 'x-correlation-id',
  AUTHORIZATION: 'authorization',
  CONTENT_TYPE: 'content-type',
  USER_AGENT: 'user-agent',
} as const;

// HTTP Status Messages
export const STATUS_MESSAGES = {
  OK: 'ok',
  DEGRADED: 'degraded',
  ERROR: 'error',
} as const;

// Service Information
export const SERVICE_INFO = {
  NAME: 'tms-api',
  DEFAULT_VERSION: '0.0.1',
} as const;

// Database Constants
export const DATABASE = {
  HEALTH_CHECK_QUERY: 'SELECT 1',
  DEFAULT_POOL_SIZE: 10,
  DEFAULT_IDLE_TIMEOUT: 1000,
} as const;

// Authentication Constants
export const AUTH = {
  STRATEGY_NAME: 'basic',
  // SECURITY: No default credentials - must be provided via environment variables
  // Use AUTH_USERNAME and AUTH_PASSWORD environment variables
} as const;

// Environment Constants
export const ENVIRONMENT = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test',
} as const;

// Default Values (Non-sensitive configuration only)
export const DEFAULTS = {
  PORT: 3000,
  DB_HOST: 'localhost',
  DB_PORT: 5432,
  DB_DATABASE: 'tms-database',
  MINIO_ENDPOINT: 'localhost',
  MINIO_PORT: 9000,
  // SECURITY: Sensitive credentials removed - must be provided via environment variables
  // Required environment variables:
  // - DB_USERNAME, DB_PASSWORD
  // - MINIO_ACCESS_KEY, MINIO_SECRET_KEY
  // - AUTH_USERNAME, AUTH_PASSWORD
} as const;
