/**
 * App Controller Tests
 *
 * These tests verify the functionality of the AppController.
 * While this controller doesn't require database access, we use the shared
 * test module factory for consistency with other tests.
 */

import { TestingModule } from '@nestjs/testing';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { createTestModule } from '../test/utils/test-module-factory';

describe('AppController', () => {
  let appController: AppController;
  let module: TestingModule;

  beforeEach(async () => {
    // Use the shared test module factory for consistency
    // Note: This controller doesn't need database access, but we use the factory
    // for consistency with other tests and to ensure proper resource cleanup
    module = await createTestModule({
      controllers: [AppController],
      providers: [AppService],
      // Disable database synchronization since we don't need it for this test
      enableSync: false,
    });

    appController = module.get<AppController>(AppController);
  });

  // Use afterAll instead of afterEach to ensure cleanup happens once at the end
  afterAll(async () => {
    try {
      // Close the module to release resources
      if (module) {
        await module.close();
      }
    } catch (error) {
      console.error('Error during test cleanup:', error);
    }
  });

  describe('root', () => {
    it('should return "Hello World!"', () => {
      expect(appController.getHello()).toBe('Hello World!');
    });
  });
});
