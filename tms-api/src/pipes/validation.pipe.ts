/**
 * Global Validation Pipe
 *
 * This pipe provides comprehensive validation for all incoming requests.
 * It uses class-validator and class-transformer to validate DTOs and
 * provides detailed error messages for validation failures.
 *
 * Features:
 * - Automatic transformation of query parameters and body data
 * - Detailed validation error messages
 * - Whitelist validation to strip unknown properties
 * - Transform validation to convert string numbers to actual numbers
 * - Custom error formatting for better API responses
 */

import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  BadRequestException,
} from '@nestjs/common';
import { validate, ValidationError } from 'class-validator';
import { plainToClass } from 'class-transformer';

@Injectable()
export class ValidationPipe implements PipeTransform<unknown> {
  async transform(
    value: unknown,
    { metatype }: ArgumentMetadata,
  ): Promise<unknown> {
    // Skip validation for primitive types and built-in types
    if (!metatype || !this.toValidate(metatype)) {
      return value;
    }

    // Handle null and undefined values
    let processedValue = value;
    if (value === null || value === undefined) {
      processedValue = {};
    }

    // Transform plain object to class instance
    const object = plainToClass(metatype, processedValue) as object;

    // Validate the transformed object
    const errors: ValidationError[] = await validate(object, {
      // Remove properties that are not defined in the DTO
      whitelist: true,
      // Throw error if non-whitelisted properties are found
      forbidNonWhitelisted: true,
      // Stop validation on first error for better performance
      stopAtFirstError: false,
    });

    if (errors.length > 0) {
      // Format validation errors into a user-friendly message
      const errorMessages = this.formatValidationErrors(errors);
      throw new BadRequestException({
        statusCode: 400,
        message: 'Validation failed',
        details: errorMessages,
      });
    }

    return object;
  }

  /**
   * Check if the metatype should be validated
   */
  private toValidate(metatype: new (...args: unknown[]) => unknown): boolean {
    const types: (new (...args: unknown[]) => unknown)[] = [
      String,
      Boolean,
      Number,
      Array,
      Object,
    ];
    return !types.includes(metatype);
  }

  /**
   * Format validation errors into a structured format
   */
  private formatValidationErrors(errors: ValidationError[]): Array<{
    property: string;
    value: unknown;
    constraints: string[];
    children?: Array<{
      property: string;
      value: unknown;
      constraints: string[];
      children?: unknown[];
    }>;
  }> {
    return errors.map((error: ValidationError) => {
      const constraints = error.constraints || {};
      const children = error.children || [];

      const formattedError: {
        property: string;
        value: unknown;
        constraints: string[];
        children?: Array<{
          property: string;
          value: unknown;
          constraints: string[];
          children?: unknown[];
        }>;
      } = {
        property: error.property,
        value: error.value,
        constraints: Object.values(constraints),
      };

      // Handle nested validation errors
      if (children.length > 0) {
        formattedError.children = this.formatValidationErrors(children);
      }

      return formattedError;
    });
  }
}
