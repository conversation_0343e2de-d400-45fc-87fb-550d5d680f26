/**
 * Validation Pipe Tests
 *
 * These tests verify the behavior of the global validation pipe.
 * They test transformation, validation, error formatting, and edge cases.
 */

import 'reflect-metadata';
import { BadRequestException } from '@nestjs/common';
import { ValidationPipe } from './validation.pipe';
import { IsString, IsInt, IsOptional, Min, Max } from 'class-validator';
import { Type, Transform } from 'class-transformer';

// Test DTO for validation pipe testing
class TestDto {
  @IsString()
  @Transform(({ value }: { value: unknown }) =>
    typeof value === 'string' ? value.trim() : value,
  )
  name!: string;

  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  age!: number;

  @IsOptional()
  @IsString()
  email?: string;
}

describe('ValidationPipe', () => {
  let pipe: ValidationPipe;

  beforeEach(() => {
    pipe = new ValidationPipe();
  });

  describe('Valid transformations', () => {
    it('should transform and validate a valid object', async () => {
      const input = {
        name: '<PERSON>',
        age: '25',
        email: '<EMAIL>',
      };

      const result = (await pipe.transform(input, {
        type: 'body',
        metatype: TestDto,
      })) as TestDto;

      expect(result).toBeInstanceOf(TestDto);
      expect(result.name).toBe('John Doe');
      expect(result.age).toBe(25); // Should be transformed from string to number
      expect(result.email).toBe('<EMAIL>');
    });

    it('should handle optional fields correctly', async () => {
      const input = {
        name: 'John Doe',
        age: '25',
        // email omitted
      };

      const result = (await pipe.transform(input, {
        type: 'body',
        metatype: TestDto,
      })) as TestDto;

      expect(result).toBeInstanceOf(TestDto);
      expect(result.name).toBe('John Doe');
      expect(result.age).toBe(25);
      expect(result.email).toBeUndefined();
    });

    it('should trim whitespace from string fields', async () => {
      const input = {
        name: '  John Doe  ',
        age: '25',
        email: '  <EMAIL>  ',
      };

      const result = (await pipe.transform(input, {
        type: 'body',
        metatype: TestDto,
      })) as TestDto;

      expect(result.name).toBe('John Doe');
      expect(result.email).toBe('  <EMAIL>  '); // email doesn't have trim transform
    });

    it('should skip validation for primitive types', async () => {
      const stringValue = 'test string';
      const numberValue = 123;
      const booleanValue = true;

      const stringResult = await pipe.transform(stringValue, {
        type: 'body',
        metatype: String,
      });
      const numberResult = await pipe.transform(numberValue, {
        type: 'body',
        metatype: Number,
      });
      const booleanResult = await pipe.transform(booleanValue, {
        type: 'body',
        metatype: Boolean,
      });

      expect(stringResult).toBe(stringValue);
      expect(numberResult).toBe(numberValue);
      expect(booleanResult).toBe(booleanValue);
    });

    it('should skip validation when no metatype is provided', async () => {
      const input = { arbitrary: 'data' };

      const result = await pipe.transform(input, {
        type: 'body',
        metatype: undefined,
      });

      expect(result).toEqual(input);
    });
  });

  describe('Validation errors', () => {
    it('should throw BadRequestException for validation failures', async () => {
      const input = {
        name: '', // Invalid: empty string
        age: 'not-a-number', // Invalid: not a number
        email: '<EMAIL>',
      };

      await expect(
        pipe.transform(input, { type: 'body', metatype: TestDto }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should format validation errors correctly', async () => {
      const input = {
        name: '', // Invalid: empty string
        age: '150', // Invalid: exceeds maximum
        email: '<EMAIL>',
      };

      try {
        await pipe.transform(input, { type: 'body', metatype: TestDto });
        throw new Error('Expected BadRequestException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(BadRequestException);

        const response = (error as BadRequestException).getResponse() as {
          statusCode: number;
          message: string;
          details: Array<{
            property: string;
            value: unknown;
            constraints: string[];
          }>;
        };
        expect(response).toHaveProperty('statusCode', 400);
        expect(response).toHaveProperty('message', 'Validation failed');
        expect(response).toHaveProperty('details');
        expect(Array.isArray(response.details)).toBe(true);
        expect(response.details.length).toBeGreaterThan(0);
      }
    });

    it('should include property names and values in error details', async () => {
      const input = {
        name: 123, // Invalid: not a string
        age: '25',
        email: '<EMAIL>',
      };

      await expect(
        pipe.transform(input, { type: 'body', metatype: TestDto }),
      ).rejects.toThrow(BadRequestException);

      try {
        await pipe.transform(input, { type: 'body', metatype: TestDto });
      } catch (error) {
        expect(error).toBeInstanceOf(BadRequestException);
        const response = (error as BadRequestException).getResponse() as {
          details: Array<{
            property: string;
            value: unknown;
            constraints: string[];
          }>;
        };
        const details = response.details;

        const nameError = details.find((detail) => detail.property === 'name');
        expect(nameError).toBeDefined();
        expect(nameError?.value).toBe(123);
        expect(nameError?.constraints).toBeInstanceOf(Array);
        expect(nameError?.constraints.length).toBeGreaterThan(0);
      }
    });

    it('should handle multiple validation errors', async () => {
      const input = {
        name: '', // Invalid: empty string
        age: '0', // Invalid: below minimum
        email: '<EMAIL>',
      };

      try {
        await pipe.transform(input, { type: 'body', metatype: TestDto });
        throw new Error('Expected BadRequestException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(BadRequestException);
        const response = (error as BadRequestException).getResponse() as {
          details: Array<{
            property: string;
            value: unknown;
            constraints: string[];
          }>;
        };
        const details = response.details;

        expect(details.length).toBeGreaterThanOrEqual(1);

        const propertyNames = details.map((detail) => detail.property);
        // Should contain at least one of the invalid properties
        expect(
          propertyNames.some((name: string) => ['name', 'age'].includes(name)),
        ).toBe(true);
      }
    });

    it('should reject unknown properties when forbidNonWhitelisted is true', async () => {
      const input = {
        name: 'John Doe',
        age: '25',
        email: '<EMAIL>',
        unknownProperty: 'should be rejected', // This should cause validation to fail
      };

      await expect(
        pipe.transform(input, { type: 'body', metatype: TestDto }),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('Type transformation', () => {
    it('should transform string numbers to actual numbers', async () => {
      const input = {
        name: 'John Doe',
        age: '25', // String that should become number
        email: '<EMAIL>',
      };

      const result = (await pipe.transform(input, {
        type: 'body',
        metatype: TestDto,
      })) as TestDto;

      expect(typeof result.age).toBe('number');
      expect(result.age).toBe(25);
    });

    it('should handle invalid number transformations', async () => {
      const input = {
        name: 'John Doe',
        age: 'not-a-number',
        email: '<EMAIL>',
      };

      await expect(
        pipe.transform(input, { type: 'body', metatype: TestDto }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should validate transformed values', async () => {
      const input = {
        name: 'John Doe',
        age: '150', // Valid number but exceeds maximum
        email: '<EMAIL>',
      };

      await expect(
        pipe.transform(input, { type: 'body', metatype: TestDto }),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('Edge cases', () => {
    it('should handle null input', async () => {
      // Null input should be converted to empty object and then validated
      // This will likely fail validation since required fields are missing
      await expect(
        pipe.transform(null, { type: 'body', metatype: TestDto }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle undefined input', async () => {
      // Undefined input should be converted to empty object and then validated
      // This will likely fail validation since required fields are missing
      await expect(
        pipe.transform(undefined, { type: 'body', metatype: TestDto }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle empty object', async () => {
      const input = {};

      await expect(
        pipe.transform(input, { type: 'body', metatype: TestDto }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle arrays', async () => {
      const input = ['not', 'an', 'object'];

      const result = await pipe.transform(input, {
        type: 'body',
        metatype: Array,
      });
      expect(result).toEqual(input);
    });
  });
});
