/**
 * TypeORM Configuration
 *
 * This file contains the TypeORM configuration for migrations.
 * It exports a DataSource that can be used by TypeORM CLI to run migrations.
 */

import { DataSource, DataSourceOptions } from 'typeorm';
import { config } from 'dotenv';
import { Material, Quiz, QuizAsset } from '../entities';

// Load environment variables from .env file
config();

// Validate required environment variables
const username = process.env.DB_USERNAME;
const password = process.env.DB_PASSWORD;
const database = process.env.DB_DATABASE;

if (!username || !password) {
  throw new Error(
    'SECURITY ERROR: DB_USERNAME and DB_PASSWORD environment variables are required for TypeORM configuration. ' +
      'No default credentials are provided for security reasons.',
  );
}

if (!database) {
  throw new Error(
    'CONFIGURATION ERROR: DB_DATABASE environment variable is required.',
  );
}

// Define the DataSource options for migrations
export const dataSourceOptions: DataSourceOptions = {
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username,
  password,
  database,
  entities: [Material, Quiz, QuizAsset],
  // migrations: ['src/migrations/*.ts'], // Uncomment when migrations are needed
  // migrationsTableName: 'migrations',
  synchronize: process.env.DB_SYNC === 'true' || false,
  logging: process.env.DB_LOGGING === 'true' || false,
};

// Create and export a DataSource instance
const dataSource = new DataSource(dataSourceOptions);
export default dataSource;
