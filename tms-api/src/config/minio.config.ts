/**
 * Minio Configuration
 *
 * This file contains the Minio configuration for object storage.
 * It provides configuration options for connecting to Minio server.
 */

import { registerAs } from '@nestjs/config';

export interface MinioConfig {
  endPoint: string;
  port: number;
  useSSL: boolean;
  accessKey: string;
  secretKey: string;
  defaultBucket: string;
}

export default registerAs('minio', (): MinioConfig => {
  const accessKey = process.env.MINIO_ACCESS_KEY;
  const secretKey = process.env.MINIO_SECRET_KEY;
  const defaultBucket = process.env.MINIO_DEFAULT_BUCKET;

  if (!accessKey || !secretKey) {
    throw new Error(
      'SECURITY ERROR: MINIO_ACCESS_KEY and MINIO_SECRET_KEY environment variables are required. ' +
        'No default credentials are provided for security reasons.',
    );
  }

  if (!defaultBucket) {
    throw new Error(
      'CONFIGURATION ERROR: MINIO_DEFAULT_BUCKET environment variable is required.',
    );
  }

  return {
    endPoint: process.env.MINIO_ENDPOINT || 'localhost',
    port: parseInt(process.env.MINIO_PORT || '9000', 10),
    useSSL: process.env.MINIO_USE_SSL === 'true',
    accessKey,
    secretKey,
    defaultBucket,
  };
});
