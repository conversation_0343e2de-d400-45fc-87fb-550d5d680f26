/**
 * Minio Module Tests
 *
 * These tests verify that the Minio module can be compiled and provides
 * the expected services.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { MinioModule } from './minio.module';
import { MinioService } from './minio.service';
import minioConfig from '../config/minio.config';
import { cleanupDatabase } from '../../test/utils/database-cleanup';

describe('MinioModule', () => {
  let module: TestingModule;

  beforeAll(async () => {
    // Environment variables are now loaded from .env file

    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          load: [minioConfig],
        }),
        MinioModule,
      ],
    }).compile();
  });

  afterAll(async () => {
    await cleanupDatabase(null, module);
  });

  it('should compile the module', () => {
    expect(module).toBeDefined();
  });

  it('should provide MinioService', () => {
    const minioService = module.get<MinioService>(MinioService);
    expect(minioService).toBeDefined();
    expect(minioService).toBeInstanceOf(MinioService);
  });
});
