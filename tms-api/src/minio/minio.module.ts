/**
 * Minio Module
 *
 * This module provides Minio object storage functionality for the TMS API.
 * It configures the Minio client and provides services for file operations.
 */

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MinioService } from './minio.service';
import minioConfig from '../config/minio.config';

@Module({
  imports: [ConfigModule.forFeature(minioConfig)],
  providers: [MinioService],
  exports: [MinioService],
})
export class MinioModule {}
