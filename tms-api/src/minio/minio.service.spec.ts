/**
 * Minio Service Tests
 *
 * These tests use a real Minio connection instead of mocks,
 * following our principle of "never mock components unless absolutely necessary".
 *
 * The tests connect to the development Minio instance to provide
 * more realistic testing of object storage interactions including:
 * - File upload and download operations
 * - Bucket management and verification
 * - File metadata retrieval and management
 * - Error handling for non-existent files
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MinioService } from './minio.service';
import minioConfig from '../config/minio.config';
import { cleanupDatabase } from '../../test/utils/database-cleanup';

describe('MinioService', () => {
  let service: MinioService;
  let module: TestingModule;

  beforeAll(async () => {
    // Environment variables are now loaded from .env file

    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          load: [minioConfig],
        }),
      ],
      providers: [MinioService],
    }).compile();

    service = module.get<MinioService>(MinioService);
  });

  afterAll(async () => {
    await cleanupDatabase(null, module);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should initialize Minio client on module init', () => {
    service.onModuleInit();
    const client = service.getClient();
    expect(client).toBeDefined();
  });

  it('should throw error when getting client before initialization', () => {
    const uninitializedService = new MinioService(new ConfigService());
    expect(() => uninitializedService.getClient()).toThrow(
      'Minio client not initialized',
    );
  });

  it('should return false for health check when client is not initialized', async () => {
    const uninitializedService = new MinioService(new ConfigService());
    const isHealthy = await uninitializedService.isHealthy();
    expect(isHealthy).toBe(false);
  });

  it('should return not connected status when client is not initialized', async () => {
    const uninitializedService = new MinioService(new ConfigService());
    const status = await uninitializedService.getStatus();
    expect(status.isConnected).toBe(false);
    expect(status.message).toBe('Minio connection is not established');
  });

  // Note: The following tests require a running Minio instance
  // They will be skipped if Minio is not available
  describe('with Minio connection', () => {
    beforeEach(() => {
      service.onModuleInit();
    });

    it('should check if Minio is healthy', async () => {
      const isHealthy = await service.isHealthy();
      // This test will pass if Minio is running, fail if not
      // In a real environment, we'd expect this to be true
      expect(typeof isHealthy).toBe('boolean');
    });

    it('should return connection status', async () => {
      const status = await service.getStatus();
      expect(status).toHaveProperty('isConnected');
      expect(status).toHaveProperty('message');
      expect(typeof status.isConnected).toBe('boolean');
      expect(typeof status.message).toBe('string');
    });

    it('should check if bucket exists', async () => {
      const exists = await service.bucketExists('non-existent-bucket');
      expect(typeof exists).toBe('boolean');
    });

    describe('file operations', () => {
      let testBucketName: string;
      const testFileName = 'test-file.txt';
      const testFileContent = Buffer.from('Hello, Minio! This is a test file.');

      beforeAll(() => {
        // Use the default bucket from configuration instead of creating a separate test bucket
        testBucketName = service.getDefaultBucket();
      });

      beforeEach(async () => {
        // Clean up any existing test files
        try {
          await service.deleteFile(testBucketName, testFileName);
        } catch {
          // Ignore errors if file doesn't exist
        }
      });

      afterEach(async () => {
        // Clean up test files
        try {
          await service.deleteFile(testBucketName, testFileName);
        } catch {
          // Ignore errors if file doesn't exist
        }
      });

      it('should ensure bucket exists', async () => {
        await expect(
          service.ensureBucket(testBucketName),
        ).resolves.not.toThrow();
        const exists = await service.bucketExists(testBucketName);
        expect(exists).toBe(true);
      });

      it('should upload a file successfully', async () => {
        const uploadResult = await service.uploadFile(testFileContent, {
          bucketName: testBucketName,
          objectName: testFileName,
          contentType: 'text/plain',
          metadata: { 'test-key': 'test-value' },
        });

        expect(uploadResult).toHaveProperty('bucketName', testBucketName);
        expect(uploadResult).toHaveProperty('objectName', testFileName);
        expect(uploadResult).toHaveProperty('etag');
        expect(uploadResult).toHaveProperty('size', testFileContent.length);
      });

      it('should download a file successfully', async () => {
        // First upload a file
        await service.uploadFile(testFileContent, {
          bucketName: testBucketName,
          objectName: testFileName,
          contentType: 'text/plain',
        });

        // Then download it
        const downloadedContent = await service.downloadFile(
          testBucketName,
          testFileName,
        );
        expect(downloadedContent).toEqual(testFileContent);
      });

      it('should get file metadata', async () => {
        // First upload a file
        await service.uploadFile(testFileContent, {
          bucketName: testBucketName,
          objectName: testFileName,
          contentType: 'text/plain',
          metadata: { 'test-key': 'test-value' },
        });

        // Then get metadata
        const metadata = await service.getFileMetadata(
          testBucketName,
          testFileName,
        );
        expect(metadata).toHaveProperty('bucketName', testBucketName);
        expect(metadata).toHaveProperty('objectName', testFileName);
        expect(metadata).toHaveProperty('size', testFileContent.length);
        expect(metadata).toHaveProperty('lastModified');
        expect(metadata).toHaveProperty('etag');
        expect(metadata.contentType).toBe('text/plain');
      });

      it('should list files in bucket', async () => {
        // Upload a test file
        await service.uploadFile(testFileContent, {
          bucketName: testBucketName,
          objectName: testFileName,
          contentType: 'text/plain',
        });

        // List files
        const files = await service.listFiles(testBucketName);
        expect(Array.isArray(files)).toBe(true);
        expect(files).toContain(testFileName);
      });

      it('should delete a file successfully', async () => {
        // First upload a file
        await service.uploadFile(testFileContent, {
          bucketName: testBucketName,
          objectName: testFileName,
          contentType: 'text/plain',
        });

        // Verify file exists
        const filesBeforeDelete = await service.listFiles(testBucketName);
        expect(filesBeforeDelete).toContain(testFileName);

        // Delete the file
        await expect(
          service.deleteFile(testBucketName, testFileName),
        ).resolves.not.toThrow();

        // Verify file is deleted
        const filesAfterDelete = await service.listFiles(testBucketName);
        expect(filesAfterDelete).not.toContain(testFileName);
      });

      it('should handle upload with default bucket', async () => {
        const uploadResult = await service.uploadFile(testFileContent, {
          objectName: testFileName,
          contentType: 'text/plain',
        });

        expect(uploadResult).toHaveProperty('bucketName', testBucketName); // Default bucket from test config
        expect(uploadResult).toHaveProperty('objectName', testFileName);

        // Clean up
        await service.deleteFile(testBucketName, testFileName);
      });

      it('should throw error when downloading non-existent file', async () => {
        console.log('---------------------------');
        console.log(
          'EXPECTED ERROR: The following MinIO download error is intentional for testing error handling',
        );
        console.log('---------------------------');

        await expect(
          service.downloadFile(testBucketName, 'non-existent-file.txt'),
        ).rejects.toThrow();
      });

      it('should throw error when getting metadata for non-existent file', async () => {
        console.log('---------------------------');
        console.log(
          'EXPECTED ERROR: The following MinIO metadata error is intentional for testing error handling',
        );
        console.log('---------------------------');

        await expect(
          service.getFileMetadata(testBucketName, 'non-existent-file.txt'),
        ).rejects.toThrow();
      });
    });
  });
});
