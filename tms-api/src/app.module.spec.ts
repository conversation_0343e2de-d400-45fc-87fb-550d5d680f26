/**
 * App Module Tests
 *
 * These tests use a real PostgreSQL database connection instead of mocks,
 * following our principle of "never mock components unless absolutely necessary".
 *
 * The tests connect to the development database (tms-dev-postgres-database)
 * to provide more realistic testing of database interactions.
 */

import { TestingModule } from '@nestjs/testing';
import { AppModule } from './app.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { HealthController } from './health/health.controller';
import { HealthService } from './health/health.service';
import { DataSource } from 'typeorm';
import { cleanupDatabase } from '../test/utils/database-cleanup';
import { createTestModule } from '../test/utils/test-module-factory';

// Interface for database query result
interface DatabaseNameResult {
  current_database: string;
}

// Type assertion function for database query results
function assertDatabaseNameResult(result: unknown): DatabaseNameResult[] {
  return result as DatabaseNameResult[];
}

describe('AppModule', () => {
  let dataSource: DataSource;
  let module: TestingModule;

  // Use the shared cleanup utility for consistent cleanup behavior
  afterAll(async () => {
    await cleanupDatabase(dataSource, module);
  }, 60000); // 60 second timeout

  it('should compile the module and connect to the database', async () => {
    // Create a module with a real database connection using the shared factory
    module = await createTestModule({
      imports: [AppModule],
      isGlobalConfig: true,
    });

    expect(module).toBeDefined();
    expect(module.get(AppController)).toBeInstanceOf(AppController);
    expect(module.get(AppService)).toBeInstanceOf(AppService);
    expect(module.get(HealthController)).toBeInstanceOf(HealthController);
    expect(module.get(HealthService)).toBeInstanceOf(HealthService);

    // Get the database connection and verify it's initialized
    dataSource = module.get(DataSource);
    expect(dataSource).toBeDefined();
    expect(dataSource.isInitialized).toBe(true);

    // Verify the database connection is using the development database
    const dbName = assertDatabaseNameResult(
      await dataSource.query('SELECT current_database()'),
    );
    const dbResult: DatabaseNameResult = dbName[0];
    expect(dbResult.current_database).toBe('tms-dev-postgres-database');
  });
});
