import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from './pipes/validation.pipe';
import { GlobalExceptionFilter } from './filters/global-exception.filter';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { ConfigService } from '@nestjs/config';

import { DEFAULTS } from './common/constants';
import helmet from 'helmet';
import compression from 'compression';
import {
  ErrorResponseSchema,
  ValidationErrorDetailSchema,
  ValidationErrorResponseSchema,
  AuthenticationErrorResponseSchema,
  NotFoundErrorResponseSchema,
  FileTooLargeErrorResponseSchema,
  InternalServerErrorResponseSchema,
} from './schemas';

/**
 * Get CORS configuration based on environment
 */
function getCorsConfiguration(configService: ConfigService) {
  const isProduction = configService.get<string>('NODE_ENV') === 'production';
  const corsOrigin = configService.get<string>('CORS_ORIGIN');

  if (isProduction && corsOrigin) {
    return {
      origin: corsOrigin,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Correlation-ID',
        'Accept',
      ],
    };
  }

  // Development configuration - more permissive
  return {
    origin: true,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Correlation-ID',
      'Accept',
    ],
  };
}

/**
 * Get security headers configuration
 */
function getSecurityHeaders(configService: ConfigService) {
  const isProduction = configService.get<string>('NODE_ENV') === 'production';

  return {
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'"], // Needed for Swagger UI
        styleSrc: ["'self'", "'unsafe-inline'"], // Needed for Swagger UI
        imgSrc: ["'self'", 'data:', 'https:'],
        fontSrc: ["'self'"],
        connectSrc: ["'self'"],
        frameSrc: ["'none'"],
        objectSrc: ["'none'"],
        baseUri: ["'self'"],
        formAction: ["'self'"],
      },
    },
    crossOriginEmbedderPolicy: false, // Disabled for Swagger UI compatibility
    crossOriginOpenerPolicy: false,
    crossOriginResourcePolicy: { policy: 'cross-origin' as const },
    dnsPrefetchControl: { allow: false },
    frameguard: { action: 'deny' as const },
    hidePoweredBy: true,
    hsts: isProduction
      ? {
          maxAge: 31536000, // 1 year
          includeSubDomains: true,
          preload: true,
        }
      : false,
    ieNoOpen: true,
    noSniff: true,
    originAgentCluster: true,
    permittedCrossDomainPolicies: false,
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' as const },
    xssFilter: true,
  };
}

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  try {
    const app = await NestFactory.create(AppModule);

    // Get configuration service
    const configService = app.get(ConfigService);

    // Configure security middleware (helmet) with custom configuration
    app.use(helmet(getSecurityHeaders(configService)));

    // Enable compression for better performance
    app.use(compression());

    // Configure CORS
    app.enableCors(getCorsConfiguration(configService));

    // Configure global validation pipe for all endpoints
    app.useGlobalPipes(new ValidationPipe());

    // Configure global exception filter for standardized error responses
    app.useGlobalFilters(new GlobalExceptionFilter());

    // Configure global interceptors for request/response logging
    app.useGlobalInterceptors(new LoggingInterceptor());

    // Configure Swagger/OpenAPI documentation
    const config = new DocumentBuilder()
      .setTitle('Teaching Material System (TMS) REST API')
      .setDescription(
        'A centralized service for storing, retrieving, updating, and deleting F2F paperless marking worked solution quiz materials',
      )
      .setVersion('0.0.1')
      .addBasicAuth(
        {
          type: 'http',
          scheme: 'basic',
          description: 'Basic Authentication using username and password',
        },
        'basic',
      )
      .addApiKey(
        {
          type: 'apiKey',
          name: 'X-Correlation-ID',
          in: 'header',
          description: 'Correlation ID for request tracing (UUID format)',
        },
        'correlation-id',
      )
      .build();

    const document = SwaggerModule.createDocument(app, config, {
      extraModels: [
        // Include error response schemas for documentation
        ErrorResponseSchema,
        ValidationErrorDetailSchema,
        ValidationErrorResponseSchema,
        AuthenticationErrorResponseSchema,
        NotFoundErrorResponseSchema,
        FileTooLargeErrorResponseSchema,
        InternalServerErrorResponseSchema,
      ],
    });
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
      },
    });

    const port = process.env.PORT || DEFAULTS.PORT;
    await app.listen(port);

    logger.log(`Application is running on: http://localhost:${port}`);
    logger.log(`Health check available at: http://localhost:${port}/health`);
    logger.log(
      `API documentation available at: http://localhost:${port}/api/docs`,
    );
  } catch (error) {
    logger.error(
      'Failed to start application:',
      error instanceof Error ? error.message : String(error),
    );
    process.exit(1);
  }
}

// Start the application with proper error handling
bootstrap().catch((error) => {
  const logger = new Logger('Bootstrap');
  logger.error(
    'Unhandled error during bootstrap:',
    error instanceof Error ? error.message : String(error),
  );
  process.exit(1);
});
