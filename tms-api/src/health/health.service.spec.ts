/**
 * Health Service Tests
 *
 * These tests use a real PostgreSQL database connection instead of mocks,
 * following our principle of "never mock components unless absolutely necessary".
 *
 * The tests connect to a dedicated test database instance (configured in .env)
 * to provide more realistic testing of database interactions.
 */

import { TestingModule } from '@nestjs/testing';
import { HealthService } from './health.service';
import { Logger } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { MinioService } from '../minio/minio.service';
import { DataSource } from 'typeorm';
import { cleanupDatabase } from '../../test/utils/database-cleanup';
import { createTestModule } from '../../test/utils/test-module-factory';
import { ConfigModule } from '@nestjs/config';
import minioConfig from '../config/minio.config';

describe('HealthService', () => {
  let service: HealthService;
  let databaseService: DatabaseService;
  let minioService: MinioService;
  let dataSource: DataSource;
  let loggerSpy: jest.SpyInstance;
  let originalEnv: NodeJS.ProcessEnv;

  let module: TestingModule;

  beforeAll(async () => {
    // Save original environment
    originalEnv = process.env;

    // Environment variables are now loaded from .env file

    // Create a module with a real database connection using the shared factory
    module = await createTestModule({
      imports: [ConfigModule.forFeature(minioConfig)],
      providers: [HealthService, DatabaseService, MinioService],
      enableLogging: false,
    });

    service = module.get<HealthService>(HealthService);
    databaseService = module.get<DatabaseService>(DatabaseService);
    minioService = module.get<MinioService>(MinioService);
    dataSource = module.get<DataSource>(DataSource);

    // Mock the logger to avoid console output during tests
    loggerSpy = jest
      .spyOn(Logger.prototype, 'log')
      .mockImplementation(() => {});
  });

  // Use the shared cleanup utility for consistent cleanup behavior
  afterAll(async () => {
    // Restore the logger spy
    loggerSpy.mockRestore();

    // Restore original environment
    process.env = originalEnv;

    // Use the shared cleanup utility
    await cleanupDatabase(dataSource, module);
  }, 60000); // 60 second timeout

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return health status with required fields', async () => {
    const health = await service.getHealth();

    expect(health).toHaveProperty('status');
    expect(health).toHaveProperty('timestamp');
    expect(health).toHaveProperty('service');
    expect(health).toHaveProperty('version');
    expect(health).toHaveProperty('components');
    expect(health.components).toHaveProperty('database');
    expect(health.components).toHaveProperty('minio');

    expect(health.service).toBe('tms-api');
    expect(health.components.database.status).toBe('ok');
    // Note: Minio status depends on whether Minio is running
    expect(['ok', 'error']).toContain(health.components.minio.status);
  });

  it('should log when checking system health', async () => {
    await service.getHealth();

    expect(loggerSpy).toHaveBeenCalledWith('Checking system health');
  });

  it('should return a valid ISO timestamp', async () => {
    const health = await service.getHealth();

    // Check if timestamp is a valid ISO date string
    expect(() => new Date(health.timestamp)).not.toThrow();
    expect(health.timestamp).toMatch(
      /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/,
    );
  });

  it('should use package version from environment if available', async () => {
    // Mock the environment variable
    process.env = { ...process.env, npm_package_version: '1.2.3' };

    const health = await service.getHealth();

    expect(health.version).toBe('1.2.3');
  });

  it('should use default version if package version is not available', async () => {
    // Remove the environment variable
    process.env = { ...process.env };
    delete process.env.npm_package_version;

    const health = await service.getHealth();

    expect(health.version).toBe('0.0.1');
  });

  it('should return degraded status when database is not connected', async () => {
    // Create a spy on the getStatus method to simulate a database connection failure
    const getStatusSpy = jest
      .spyOn(databaseService, 'getStatus')
      .mockResolvedValue({
        isConnected: false,
        message: 'Database connection is not established',
      });

    // Mock Minio as healthy to isolate the database test
    const minioStatusSpy = jest
      .spyOn(minioService, 'getStatus')
      .mockResolvedValue({
        isConnected: true,
        message: 'Minio connection is healthy',
      });

    const health = await service.getHealth();

    expect(health.status).toBe('degraded');
    expect(health.components.database.status).toBe('error');
    expect(health.components.database.message).toBe(
      'Database connection is not established',
    );

    // Restore the original methods
    getStatusSpy.mockRestore();
    minioStatusSpy.mockRestore();
  });

  it('should return degraded status when minio is not connected', async () => {
    // Mock database as healthy to isolate the Minio test
    const dbStatusSpy = jest
      .spyOn(databaseService, 'getStatus')
      .mockResolvedValue({
        isConnected: true,
        message: 'Database connection is healthy',
      });

    // Create a spy on the getStatus method to simulate a Minio connection failure
    const minioStatusSpy = jest
      .spyOn(minioService, 'getStatus')
      .mockResolvedValue({
        isConnected: false,
        message: 'Minio connection is not established',
      });

    const health = await service.getHealth();

    expect(health.status).toBe('degraded');
    expect(health.components.minio.status).toBe('error');
    expect(health.components.minio.message).toBe(
      'Minio connection is not established',
    );

    // Restore the original methods
    dbStatusSpy.mockRestore();
    minioStatusSpy.mockRestore();
  });
});
