/**
 * Health Module Tests
 *
 * These tests use a real PostgreSQL database connection instead of mocks,
 * following our principle of "never mock components unless absolutely necessary".
 *
 * The tests connect to a dedicated test database instance (configured in .env)
 * to provide more realistic testing of database interactions.
 */

import { TestingModule } from '@nestjs/testing';
import { HealthModule } from './health.module';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';
import { DataSource } from 'typeorm';
import { cleanupDatabaseWithCallback } from '../../test/utils/database-cleanup';
import { createTestModule } from '../../test/utils/test-module-factory';

describe('HealthModule', () => {
  let dataSource: DataSource;
  let module: TestingModule;

  // Use the shared cleanup utility for consistent cleanup behavior
  afterEach((done) => {
    cleanupDatabaseWithCallback(dataSource, module, done);
  }, 60000); // 60 second timeout

  it('should compile the module', (done) => {
    // Create a module with a real database connection using the shared factory
    createTestModule({
      imports: [HealthModule],
      isGlobalConfig: true,
    })
      .then((compiledModule) => {
        module = compiledModule;

        expect(module).toBeDefined();
        expect(module.get(HealthController)).toBeInstanceOf(HealthController);
        expect(module.get(HealthService)).toBeInstanceOf(HealthService);

        // Get the database connection
        try {
          dataSource = module.get(DataSource);
          expect(dataSource).toBeDefined();
          done();
        } catch (error) {
          console.error('Error getting DataSource:', error as Error);
          done.fail(error as Error);
        }
      })
      .catch((error: Error) => {
        console.error('Error compiling module:', error);
        done.fail(error);
      });
  }, 60000); // Increase timeout to 60 seconds
});
