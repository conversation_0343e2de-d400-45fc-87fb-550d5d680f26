/**
 * Quiz Controller
 *
 * This controller handles HTTP requests for quiz-related operations.
 * It implements the GET /quiz/f2f/paperless-marking-worked-solutions endpoint
 * as specified in the Design Doc.
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpStatus,
  HttpException,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';

import {
  ApiTags,
  ApiBasicAuth,
  ApiHeader,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiConsumes,
  ApiParam,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import type { Express } from 'express';
import { QuizService } from './quiz.service';
import {
  QuizQueryDto,
  QuizResponseDto,
  QuizUploadDto,
  QuizUploadResponseDto,
  QuizUpdateDto,
} from '../dto';
import { BasicAuthGuard } from '../auth/guards/basic-auth.guard';
import { CorrelationId } from '../decorators/correlation-id.decorator';
import { CommonErrorResponses } from '../schemas/error-response.schema';
import { sanitizeFileUpload } from '../utils/security.utils';
import { CustomThrottlerGuard } from '../guards/throttle.guard';

@ApiTags('Quiz')
@ApiBasicAuth('basic')
@ApiHeader({
  name: 'X-Correlation-ID',
  description: 'Correlation ID for request tracing (UUID format)',
  required: true,
  schema: {
    type: 'string',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  },
})
@Controller('quiz')
@UseGuards(BasicAuthGuard, CustomThrottlerGuard)
export class QuizController {
  private readonly logger = new Logger(QuizController.name);

  constructor(private readonly quizService: QuizService) {}

  /**
   * GET /quiz/f2f/paperless-marking-worked-solutions
   *
   * Retrieves F2F paperless marking worked solution quizzes based on specified filters.
   * All query parameters are validated using QuizQueryDto.
   *
   * @param queryDto - Validated query parameters
   * @param correlationId - X-Correlation-ID header for request tracing
   * @returns QuizResponseDto with quiz metadata and GIF URLs
   */
  @Get('f2f/paperless-marking-worked-solutions')
  @ApiOperation({
    summary: 'Retrieve worked solutions for paperless marking quiz',
    description:
      'Retrieves worked solutions for one paperless marking quiz. Returns the same data contained in a QzF2f.zip file from Dr. Du, with the solution GIFs translated into pre-signed URLs from MinIO.',
  })
  @ApiQuery({
    name: 'grade',
    type: 'integer',
    description: 'Grade level (1-12)',
    example: 12,
    required: true,
  })
  @ApiQuery({
    name: 'subject',
    type: 'string',
    description: 'Subject name (e.g., Math, Phys, Chem, Engl)',
    example: 'Math',
    required: true,
  })
  @ApiQuery({
    name: 'course',
    type: 'string',
    description:
      'Course code (e.g., 3U). For Y9 and Y10 where there is no course, input as empty string',
    example: '3U',
    required: true,
  })
  @ApiQuery({
    name: 'classLevel',
    type: 'string',
    description: 'Class level identifier',
    example: 'A1',
    required: true,
  })
  @ApiQuery({
    name: 'color',
    type: 'string',
    description:
      'Quiz color code (R for red quiz). If there is only one quiz version, it will be R',
    example: 'R',
    required: true,
  })
  @ApiQuery({
    name: 'year',
    type: 'integer',
    description: 'Academic year',
    example: 2025,
    required: true,
  })
  @ApiQuery({
    name: 'term',
    type: 'integer',
    description: 'Academic term (1-4)',
    example: 2,
    required: true,
  })
  @ApiQuery({
    name: 'week',
    type: 'integer',
    description: 'Week number (1-52)',
    example: 4,
    required: true,
  })
  @ApiQuery({
    name: 'weekType',
    type: 'string',
    description: 'Week type',
    example: 'normal',
    enum: ['normal', 'holiday'],
    required: true,
  })
  @ApiQuery({
    name: 'teachingProgram',
    type: 'string',
    description: 'Teaching program name (optional)',
    example: 'St George Girls',
    required: false,
  })
  @ApiQuery({
    name: 'lessonName',
    type: 'string',
    description: 'Lesson name/topic for reference and logging (optional)',
    example: 'Formulae',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Quiz worked solutions retrieved successfully',
    type: QuizResponseDto,
  })
  @ApiResponse(CommonErrorResponses.BadRequest)
  @ApiResponse(CommonErrorResponses.Unauthorized)
  @ApiResponse(CommonErrorResponses.NotFound)
  @ApiResponse(CommonErrorResponses.InternalServerError)
  async getQuizWorkedSolutions(
    @Query() queryDto: QuizQueryDto,
    @CorrelationId() correlationId: string,
  ): Promise<QuizResponseDto> {
    this.logger.log(
      `GET quiz worked solutions request received with correlation ID: ${correlationId}`,
    );

    try {
      // Log the query parameters for debugging (excluding sensitive data)
      this.logger.debug(
        `Query parameters: grade=${queryDto.grade}, subject=${queryDto.subject}, ` +
          `classLevel=${queryDto.classLevel}, year=${queryDto.year}, term=${queryDto.term}, ` +
          `week=${queryDto.week}, weekType=${queryDto.weekType}`,
      );

      // Call the service to retrieve the quiz data
      const quizResponse = await this.quizService.findQuizWorkedSolutions(
        queryDto,
        correlationId,
      );

      this.logger.log(
        `Successfully retrieved quiz with ID: ${quizResponse.id} for correlation ID: ${correlationId}`,
      );

      return quizResponse;
    } catch (error) {
      this.logger.error(
        `Error retrieving quiz worked solutions for correlation ID: ${correlationId}`,
        error,
      );

      // Re-throw the error to be handled by the global exception filter
      if (error instanceof HttpException) {
        throw error;
      }

      // For unexpected errors, throw a generic internal server error
      throw new HttpException(
        'An unexpected error occurred while retrieving quiz data',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * POST /quiz/f2f/paperless-marking-worked-solutions
   *
   * Uploads a new F2F paperless marking worked solution quiz.
   * Processes ZIP file containing LessonMetadata.json, QzF2f.json, and solution GIF files.
   *
   * @param uploadDto - Validated query parameters for upload
   * @param file - Uploaded ZIP file
   * @param correlationId - X-Correlation-ID header for request tracing
   * @returns QuizUploadResponseDto with upload confirmation details
   */
  @Post('f2f/paperless-marking-worked-solutions')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({
    summary: 'Upload worked solutions for paperless marking quiz',
    description:
      'Uploads a new F2F paperless marking worked solution quiz. Processes ZIP file containing LessonMetadata.json, QzF2f.json, and solution GIF files.',
  })
  @ApiConsumes('multipart/form-data')
  @ApiQuery({
    name: 'year',
    type: 'integer',
    description: 'Academic year',
    example: 2025,
    required: true,
  })
  @ApiQuery({
    name: 'term',
    type: 'integer',
    description: 'Academic term (1-4)',
    example: 2,
    required: true,
  })
  @ApiQuery({
    name: 'week',
    type: 'integer',
    description: 'Week number (1-52)',
    example: 4,
    required: true,
  })
  @ApiQuery({
    name: 'weekType',
    type: 'string',
    description: 'Week type',
    example: 'normal',
    enum: ['normal', 'holiday'],
    required: true,
  })
  @ApiQuery({
    name: 'teachingProgram',
    type: 'string',
    description: 'Teaching program name (optional)',
    example: 'St George Girls',
    required: false,
  })
  @ApiResponse({
    status: 201,
    description: 'Quiz worked solutions uploaded and processed successfully',
    type: QuizUploadResponseDto,
  })
  @ApiResponse(CommonErrorResponses.BadRequest)
  @ApiResponse(CommonErrorResponses.Unauthorized)
  @ApiResponse(CommonErrorResponses.PayloadTooLarge)
  @ApiResponse(CommonErrorResponses.InternalServerError)
  async uploadQuizWorkedSolutions(
    @Query() uploadDto: QuizUploadDto,
    @UploadedFile() file: Express.Multer.File,
    @CorrelationId() correlationId: string,
  ): Promise<QuizUploadResponseDto> {
    this.logger.log(
      `POST quiz upload request received with correlation ID: ${correlationId}`,
    );

    try {
      // Validate file upload
      if (!file) {
        throw new HttpException(
          'No file uploaded. Please provide a ZIP file.',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Sanitize and validate file upload using security utilities
      const sanitizationResult = sanitizeFileUpload(
        file.originalname,
        file.mimetype,
      );

      if (!sanitizationResult.isValid) {
        this.logger.warn(`File upload rejected: ${sanitizationResult.reason}`, {
          correlationId,
          originalFilename: file.originalname,
          mimetype: file.mimetype,
        });
        throw new HttpException(
          sanitizationResult.reason,
          HttpStatus.BAD_REQUEST,
        );
      }

      this.logger.debug(
        `Processing ZIP file: ${file.originalname}, size: ${file.size} bytes`,
      );

      // Call the service to process the upload
      const uploadResponse = await this.quizService.uploadQuizWorkedSolutions(
        uploadDto,
        file,
        correlationId,
      );

      this.logger.log(
        `Successfully processed quiz upload for correlation ID: ${correlationId}`,
      );

      return uploadResponse;
    } catch (error) {
      this.logger.error(
        `Error processing quiz upload for correlation ID: ${correlationId}`,
        error,
      );

      // Re-throw the error to be handled by the global exception filter
      if (error instanceof HttpException) {
        throw error;
      }

      // For unexpected errors, throw a generic internal server error
      throw new HttpException(
        'An unexpected error occurred while processing the upload',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * PUT /quiz/{id}
   *
   * Updates an existing quiz with new metadata and optionally replaces the ZIP file.
   * Supports partial updates - only provided fields will be updated.
   *
   * @param id - Quiz UUID to update
   * @param updateDto - Validated query parameters for update
   * @param file - Optional uploaded ZIP file to replace existing assets
   * @param correlationId - X-Correlation-ID header for request tracing
   * @returns QuizResponseDto with updated quiz data
   */
  @Put(':id')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({
    summary: 'Update existing quiz',
    description:
      'Updates an existing quiz with new metadata and optionally replaces the ZIP file. Supports partial updates - only provided fields will be updated.',
  })
  @ApiConsumes('multipart/form-data')
  @ApiParam({
    name: 'id',
    type: 'string',
    description: 'Quiz UUID to update',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
  })
  @ApiQuery({
    name: 'year',
    type: 'integer',
    description: 'Academic year (optional)',
    example: 2025,
    required: false,
  })
  @ApiQuery({
    name: 'term',
    type: 'integer',
    description: 'Academic term 1-4 (optional)',
    example: 2,
    required: false,
  })
  @ApiQuery({
    name: 'week',
    type: 'integer',
    description: 'Week number 1-52 (optional)',
    example: 4,
    required: false,
  })
  @ApiQuery({
    name: 'weekType',
    type: 'string',
    description: 'Week type (optional)',
    example: 'normal',
    enum: ['normal', 'holiday'],
    required: false,
  })
  @ApiQuery({
    name: 'teachingProgram',
    type: 'string',
    description: 'Teaching program name (optional)',
    example: 'St George Girls',
    required: false,
  })
  @ApiQuery({
    name: 'subject',
    type: 'string',
    description: 'Subject name (optional)',
    example: 'Math',
    required: false,
  })
  @ApiQuery({
    name: 'grade',
    type: 'integer',
    description: 'Grade level 1-12 (optional)',
    example: 12,
    required: false,
  })
  @ApiQuery({
    name: 'course',
    type: 'string',
    description: 'Course code (optional)',
    example: '3U',
    required: false,
  })
  @ApiQuery({
    name: 'classLevel',
    type: 'string',
    description: 'Class level identifier (optional)',
    example: 'A1',
    required: false,
  })
  @ApiQuery({
    name: 'color',
    type: 'string',
    description: 'Quiz color code (optional)',
    example: 'R',
    required: false,
  })
  @ApiQuery({
    name: 'lessonName',
    type: 'string',
    description: 'Lesson name/topic (optional)',
    example: 'Formulae',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Quiz updated successfully',
    type: QuizResponseDto,
  })
  @ApiResponse(CommonErrorResponses.BadRequest)
  @ApiResponse(CommonErrorResponses.Unauthorized)
  @ApiResponse(CommonErrorResponses.NotFound)
  @ApiResponse(CommonErrorResponses.PayloadTooLarge)
  @ApiResponse(CommonErrorResponses.InternalServerError)
  async updateQuiz(
    @Param('id') id: string,
    @Query() updateDto: QuizUpdateDto,
    @UploadedFile() file: Express.Multer.File | undefined,
    @CorrelationId() correlationId: string,
  ): Promise<QuizResponseDto> {
    this.logger.log(
      `PUT quiz update request received for ID: ${id} with correlation ID: ${correlationId}`,
    );

    try {
      // Validate UUID format
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(id)) {
        throw new HttpException(
          'Invalid quiz ID format. Must be a valid UUID.',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Sanitize and validate file upload if file is provided
      if (file) {
        const sanitizationResult = sanitizeFileUpload(
          file.originalname,
          file.mimetype,
        );

        if (!sanitizationResult.isValid) {
          this.logger.warn(
            `File upload rejected for update: ${sanitizationResult.reason}`,
            {
              correlationId,
              quizId: id,
              originalFilename: file.originalname,
              mimetype: file.mimetype,
            },
          );
          throw new HttpException(
            sanitizationResult.reason,
            HttpStatus.BAD_REQUEST,
          );
        }
      }

      this.logger.debug(
        `Processing quiz update for ID: ${id}${file ? ` with new file: ${file.originalname}` : ' (metadata only)'}`,
      );

      // Call the service to process the update
      const updatedQuiz = await this.quizService.updateQuizWorkedSolutions(
        id,
        updateDto,
        file,
        correlationId,
      );

      this.logger.log(
        `Successfully updated quiz with ID: ${id} for correlation ID: ${correlationId}`,
      );

      return updatedQuiz;
    } catch (error) {
      this.logger.error(
        `Error updating quiz with ID: ${id} for correlation ID: ${correlationId}`,
        error,
      );

      // Re-throw the error to be handled by the global exception filter
      if (error instanceof HttpException) {
        throw error;
      }

      // For unexpected errors, throw a generic internal server error
      throw new HttpException(
        'An unexpected error occurred while updating the quiz',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * DELETE /quiz/{id}
   *
   * Deletes an existing quiz and all associated assets.
   * Removes quiz from database and cleans up associated files from MinIO storage.
   *
   * @param id - Quiz UUID to delete
   * @param correlationId - X-Correlation-ID header for request tracing
   * @returns Success message confirming deletion
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Delete quiz',
    description:
      'Deletes an existing quiz and all associated assets. Removes quiz from database and cleans up associated files from MinIO storage.',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    description: 'Quiz UUID to delete',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Quiz deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example:
            'Quiz with ID 123e4567-e89b-12d3-a456-************ has been successfully deleted',
        },
      },
    },
  })
  @ApiResponse(CommonErrorResponses.BadRequest)
  @ApiResponse(CommonErrorResponses.Unauthorized)
  @ApiResponse(CommonErrorResponses.NotFound)
  @ApiResponse(CommonErrorResponses.InternalServerError)
  async deleteQuiz(
    @Param('id') id: string,
    @CorrelationId() correlationId: string,
  ): Promise<{ message: string }> {
    this.logger.log(
      `DELETE quiz request received for ID: ${id} with correlation ID: ${correlationId}`,
    );

    try {
      // Validate UUID format
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(id)) {
        throw new HttpException(
          'Invalid quiz ID format. Must be a valid UUID.',
          HttpStatus.BAD_REQUEST,
        );
      }

      this.logger.debug(`Processing quiz deletion for ID: ${id}`);

      // Call the service to process the deletion
      await this.quizService.deleteQuizWorkedSolutions(id, correlationId);

      this.logger.log(
        `Successfully deleted quiz with ID: ${id} for correlation ID: ${correlationId}`,
      );

      return {
        message: `Quiz with ID ${id} has been successfully deleted`,
      };
    } catch (error) {
      this.logger.error(
        `Error deleting quiz with ID: ${id} for correlation ID: ${correlationId}`,
        error,
      );

      // Re-throw the error to be handled by the global exception filter
      if (error instanceof HttpException) {
        throw error;
      }

      // For unexpected errors, throw a generic internal server error
      throw new HttpException(
        'An unexpected error occurred while deleting the quiz',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
