/**
 * Quiz Integration Tests
 *
 * Integration tests for the Quiz API endpoints.
 * Tests the complete flow from HTTP request to database and back.
 */

import { INestApplication } from '@nestjs/common';
import { DataSource } from 'typeorm';
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';
import { Quiz, QuizAsset } from '../entities';
import { cleanupDatabase } from '../../test/utils/database-cleanup';
import { createTestModule } from '../../test/utils/test-module-factory';
import { TEST_CREDENTIALS } from '../../test/test-constants';
import { AppModule } from '../app.module';
import { ValidationPipe } from '../pipes/validation.pipe';
import { GlobalExceptionFilter } from '../filters/global-exception.filter';

describe('Quiz Integration Tests', () => {
  let app: INestApplication;
  let dataSource: DataSource;

  beforeAll(async () => {
    // Create test module using the full AppModule which includes middleware configuration
    const module = await createTestModule({
      imports: [AppModule],
      entities: [
        Quiz as unknown as new (...args: unknown[]) => unknown,
        QuizAsset as unknown as new (...args: unknown[]) => unknown,
      ],
      isGlobalConfig: true,
      enableLogging: false,
      enableSync: true,
      dropSchema: false,
    });

    app = module.createNestApplication();

    // Apply global pipes and filters (same as main.ts)
    app.useGlobalPipes(new ValidationPipe());
    app.useGlobalFilters(new GlobalExceptionFilter());

    await app.init();

    dataSource = module.get(DataSource);
  });

  afterAll(async () => {
    await cleanupDatabase(dataSource, app);
  });

  describe('GET /quiz/f2f/paperless-marking-worked-solutions', () => {
    const correlationId = uuidv4();
    const basicAuthCredentials = TEST_CREDENTIALS.BASIC_AUTH_HEADER;

    // Helper function to avoid unsafe argument warnings
    const makeRequest = () =>
      request(app.getHttpServer() as Parameters<typeof request>[0]);

    it('should return 400 when X-Correlation-ID header is missing', async () => {
      const response = await makeRequest()
        .get('/quiz/f2f/paperless-marking-worked-solutions')
        .set('Authorization', `Basic ${basicAuthCredentials}`)
        .query({
          grade: 12,
          subject: 'Math',
          course: '3U',
          classLevel: 'A1',
          color: 'R',
          year: 2025,
          term: 2,
          week: 4,
          weekType: 'normal',
        })
        .expect(400);

      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty(
        'message',
        'X-Correlation-ID header is required',
      );
    });

    it('should return 401 when Basic Auth is missing', async () => {
      const response = await makeRequest()
        .get('/quiz/f2f/paperless-marking-worked-solutions')
        .set('X-Correlation-ID', correlationId)
        .query({
          grade: 12,
          subject: 'Math',
          course: '3U',
          classLevel: 'A1',
          color: 'R',
          year: 2025,
          term: 2,
          week: 4,
          weekType: 'normal',
        })
        .expect(401);

      expect(response.body).toHaveProperty('statusCode', 401);
      expect(response.body).toHaveProperty('message', 'Unauthorized');
    });

    it('should return 400 when required query parameters are missing', async () => {
      const response = await makeRequest()
        .get('/quiz/f2f/paperless-marking-worked-solutions')
        .set('X-Correlation-ID', correlationId)
        .set('Authorization', `Basic ${basicAuthCredentials}`)
        .query({
          grade: 12,
          subject: 'Math',
          // Missing required parameters
        })
        .expect(400);

      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty('message', 'Validation failed');
      expect(response.body).toHaveProperty('details');
      expect(
        Array.isArray((response.body as { details: unknown }).details),
      ).toBe(true);
    });

    it('should return 400 when query parameters have invalid values', async () => {
      const response = await makeRequest()
        .get('/quiz/f2f/paperless-marking-worked-solutions')
        .set('X-Correlation-ID', correlationId)
        .set('Authorization', `Basic ${basicAuthCredentials}`)
        .query({
          grade: 15, // Invalid grade (> 12)
          subject: 'Math',
          course: '3U',
          classLevel: 'A1',
          color: 'R',
          year: 2025,
          term: 2,
          week: 4,
          weekType: 'normal',
        })
        .expect(400);

      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty('message', 'Validation failed');
      expect(response.body).toHaveProperty('details');
      expect(
        Array.isArray((response.body as { details: unknown }).details),
      ).toBe(true);
    });

    it('should return 404 when no quiz matches the criteria', async () => {
      const response = await makeRequest()
        .get('/quiz/f2f/paperless-marking-worked-solutions')
        .set('X-Correlation-ID', correlationId)
        .set('Authorization', `Basic ${basicAuthCredentials}`)
        .query({
          grade: 12,
          subject: 'NonExistentSubject',
          course: '3U',
          classLevel: 'A1',
          color: 'R',
          year: 2025,
          term: 2,
          week: 4,
          weekType: 'normal',
        })
        .expect(404);

      expect(response.body).toHaveProperty('statusCode', 404);
      expect(response.body).toHaveProperty(
        'message',
        'No quiz found matching the provided criteria',
      );
    });

    it('should return quiz data when valid parameters are provided and quiz exists', async () => {
      // This test would require setting up test data in the database
      // For now, we'll test the endpoint structure and validation
      // In a real implementation, you would:
      // 1. Insert test quiz data into the database
      // 2. Make the request
      // 3. Verify the response structure matches QuizResponseDto
      // 4. Clean up the test data

      const response = await makeRequest()
        .get('/quiz/f2f/paperless-marking-worked-solutions')
        .set('X-Correlation-ID', correlationId)
        .set('Authorization', `Basic ${basicAuthCredentials}`)
        .query({
          grade: 12,
          subject: 'Math',
          course: '3U',
          classLevel: 'A1',
          color: 'R',
          year: 2025,
          term: 2,
          week: 4,
          weekType: 'normal',
          teachingProgram: 'St George Girls',
          lessonName: 'Formulae',
        })
        .expect(404); // Expecting 404 since no test data is set up

      expect(response.body).toHaveProperty('statusCode', 404);
    });

    it('should handle optional query parameters correctly', async () => {
      const response = await makeRequest()
        .get('/quiz/f2f/paperless-marking-worked-solutions')
        .set('X-Correlation-ID', correlationId)
        .set('Authorization', `Basic ${basicAuthCredentials}`)
        .query({
          grade: 10,
          subject: 'Science',
          course: '2U',
          classLevel: 'B2',
          color: 'B',
          year: 2024,
          term: 1,
          week: 10,
          weekType: 'holiday',
          // Optional parameters omitted
        })
        .expect(404); // Expecting 404 since no test data is set up

      expect(response.body).toHaveProperty('statusCode', 404);
    });

    it('should validate weekType enum values', async () => {
      const response = await makeRequest()
        .get('/quiz/f2f/paperless-marking-worked-solutions')
        .set('X-Correlation-ID', correlationId)
        .set('Authorization', `Basic ${basicAuthCredentials}`)
        .query({
          grade: 12,
          subject: 'Math',
          course: '3U',
          classLevel: 'A1',
          color: 'R',
          year: 2025,
          term: 2,
          week: 4,
          weekType: 'invalid', // Invalid enum value
        })
        .expect(400);

      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty('message');
    });

    it('should include correlation ID in response headers', async () => {
      const response = await makeRequest()
        .get('/quiz/f2f/paperless-marking-worked-solutions')
        .set('X-Correlation-ID', correlationId)
        .set('Authorization', `Basic ${basicAuthCredentials}`)
        .query({
          grade: 12,
          subject: 'Math',
          course: '3U',
          classLevel: 'A1',
          color: 'R',
          year: 2025,
          term: 2,
          week: 4,
          weekType: 'normal',
        });

      expect(response.headers).toHaveProperty(
        'x-correlation-id',
        correlationId,
      );
    });
  });

  describe('POST /quiz/f2f/paperless-marking-worked-solutions', () => {
    const correlationId = uuidv4();
    const basicAuthCredentials = TEST_CREDENTIALS.BASIC_AUTH_HEADER;

    // Helper function to avoid unsafe argument warnings
    const makeRequest = () =>
      request(app.getHttpServer() as Parameters<typeof request>[0]);

    it('should return 400 when X-Correlation-ID header is missing', async () => {
      const response = await makeRequest()
        .post('/quiz/f2f/paperless-marking-worked-solutions')
        .set('Authorization', `Basic ${basicAuthCredentials}`)
        .query({
          year: 2025,
          term: 2,
          week: 4,
          weekType: 'normal',
        })
        .expect(400);

      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty(
        'message',
        'X-Correlation-ID header is required',
      );
    });

    it('should return 401 when Basic Auth is missing', async () => {
      const response = await makeRequest()
        .post('/quiz/f2f/paperless-marking-worked-solutions')
        .set('X-Correlation-ID', correlationId)
        .query({
          year: 2025,
          term: 2,
          week: 4,
          weekType: 'normal',
        })
        .expect(401);

      expect(response.body).toHaveProperty('statusCode', 401);
      expect(response.body).toHaveProperty('message', 'Unauthorized');
    });

    it('should return 400 when required query parameters are missing', async () => {
      const response = await makeRequest()
        .post('/quiz/f2f/paperless-marking-worked-solutions')
        .set('X-Correlation-ID', correlationId)
        .set('Authorization', `Basic ${basicAuthCredentials}`)
        .query({
          year: 2025,
          // Missing required parameters
        })
        .expect(400);

      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty('message', 'Validation failed');
      expect(response.body).toHaveProperty('details');
      expect(
        Array.isArray((response.body as { details: unknown }).details),
      ).toBe(true);
    });

    it('should return 400 when no file is uploaded', async () => {
      const response = await makeRequest()
        .post('/quiz/f2f/paperless-marking-worked-solutions')
        .set('X-Correlation-ID', correlationId)
        .set('Authorization', `Basic ${basicAuthCredentials}`)
        .query({
          year: 2025,
          term: 2,
          week: 4,
          weekType: 'normal',
        })
        .expect(400);

      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty(
        'message',
        'No file uploaded. Please provide a ZIP file.',
      );
    });

    it('should return 400 when uploaded file is not a ZIP', async () => {
      const response = await makeRequest()
        .post('/quiz/f2f/paperless-marking-worked-solutions')
        .set('X-Correlation-ID', correlationId)
        .set('Authorization', `Basic ${basicAuthCredentials}`)
        .query({
          year: 2025,
          term: 2,
          week: 4,
          weekType: 'normal',
        })
        .attach('file', Buffer.from('fake content'), 'test.txt')
        .expect(400);

      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty(
        'message',
        'File extension .txt not allowed. Only ZIP files are permitted.',
      );
    });

    it('should validate query parameter types and ranges', async () => {
      const response = await makeRequest()
        .post('/quiz/f2f/paperless-marking-worked-solutions')
        .set('X-Correlation-ID', correlationId)
        .set('Authorization', `Basic ${basicAuthCredentials}`)
        .query({
          year: 1999, // Invalid year (< 2000)
          term: 5, // Invalid term (> 4)
          week: 60, // Invalid week (> 52)
          weekType: 'invalid', // Invalid weekType
        })
        .attach('file', Buffer.from('fake zip content'), 'test.zip')
        .expect(400);

      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty('message', 'Validation failed');
      expect(response.body).toHaveProperty('details');
      expect(
        Array.isArray((response.body as { details: unknown }).details),
      ).toBe(true);
    });

    it('should include correlation ID in response headers', async () => {
      const response = await makeRequest()
        .post('/quiz/f2f/paperless-marking-worked-solutions')
        .set('X-Correlation-ID', correlationId)
        .set('Authorization', `Basic ${basicAuthCredentials}`)
        .query({
          year: 2025,
          term: 2,
          week: 4,
          weekType: 'normal',
        });
      // Note: Not attaching file to test validation first

      expect(response.headers).toHaveProperty(
        'x-correlation-id',
        correlationId,
      );
    });

    // Note: Testing with actual ZIP file upload would require:
    // 1. Creating a valid ZIP file with LessonMetadata.json, QzF2f.json, and GIF files
    // 2. Mocking MinIO service for file uploads
    // 3. Testing the complete upload flow
    // This would be implemented in a more comprehensive test suite
  });
});
