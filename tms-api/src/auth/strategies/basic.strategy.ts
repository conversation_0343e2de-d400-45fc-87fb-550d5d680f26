/**
 * Basic Authentication Strategy
 *
 * This strategy implements HTTP Basic Authentication using Passport.
 * It validates credentials against configured username/password.
 */

import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { BasicStrategy as Strategy } from 'passport-http';
import { AuthConfig } from '../../config/auth.config';

@Injectable()
export class BasicStrategy extends PassportStrategy(Strategy) {
  constructor(private configService: ConfigService) {
    super();
  }

  validate(username: string, password: string): { username: string } {
    const authConfig = this.configService.get<AuthConfig>('auth');

    if (!authConfig) {
      throw new UnauthorizedException('Authentication configuration not found');
    }

    if (username === authConfig.username && password === authConfig.password) {
      return { username };
    }

    throw new UnauthorizedException('Invalid credentials');
  }
}
