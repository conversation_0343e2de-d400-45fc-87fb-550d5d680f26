/**
 * Authentication Integration Tests
 *
 * Integration tests for authentication functionality with real HTTP requests.
 */

import { TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AppModule } from '../app.module';
import { cleanupDatabase } from '../../test/utils/database-cleanup';
import { createTestModule } from '../../test/utils/test-module-factory';
import { TEST_CREDENTIALS } from '../../test/test-constants';
import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Server } from 'http';

// Helper function to get typed HTTP server for supertest
// Since we're using Express (NestJS default), we can safely cast to Node.js Server
function getHttpServer(app: INestApplication) {
  return request(app.getHttpServer() as Server);
}

describe('Authentication Integration', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let module: TestingModule;

  beforeAll(async () => {
    // Create test module with real database connection
    module = await createTestModule({
      imports: [AppModule],
      isGlobalConfig: true,
    });

    app = module.createNestApplication();
    await app.init();

    dataSource = module.get(DataSource);
  });

  afterAll(async () => {
    await cleanupDatabase(dataSource, module);
    await app.close();
  }, 60000);

  describe('Protected endpoint /protected', () => {
    it('should return 400 when no correlation ID is provided', async () => {
      const response = await getHttpServer(app)
        .get('/protected')
        .auth(TEST_CREDENTIALS.USERNAME, TEST_CREDENTIALS.PASSWORD)
        .expect(400);

      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty(
        'message',
        'X-Correlation-ID header is required',
      );
    });

    it('should return 401 when no authentication is provided', async () => {
      const correlationId = uuidv4();
      const response = await getHttpServer(app)
        .get('/protected')
        .set('X-Correlation-ID', correlationId)
        .expect(401);

      expect(response.body).toHaveProperty('statusCode', 401);
      expect(response.body).toHaveProperty('message', 'Unauthorized');
    });

    it('should return 401 when invalid credentials are provided', async () => {
      const correlationId = uuidv4();
      const response = await getHttpServer(app)
        .get('/protected')
        .set('X-Correlation-ID', correlationId)
        .auth('wrong-user', 'wrong-password')
        .expect(401);

      expect(response.body).toHaveProperty('statusCode', 401);
      expect(response.body).toHaveProperty('message', 'Invalid credentials');
    });

    it('should return 200 when valid credentials are provided', async () => {
      const correlationId = uuidv4();
      const response = await getHttpServer(app)
        .get('/protected')
        .set('X-Correlation-ID', correlationId)
        .auth(TEST_CREDENTIALS.USERNAME, TEST_CREDENTIALS.PASSWORD)
        .expect(200);

      expect(response.body).toHaveProperty(
        'message',
        'This is a protected endpoint that requires Basic Auth!',
      );
      expect(response.body).toHaveProperty('correlationId', correlationId);
      expect(response.headers).toHaveProperty(
        'x-correlation-id',
        correlationId,
      );
    });

    it('should return 401 when only username is provided', async () => {
      const correlationId = uuidv4();
      const response = await getHttpServer(app)
        .get('/protected')
        .set('X-Correlation-ID', correlationId)
        .auth(TEST_CREDENTIALS.USERNAME, '')
        .expect(401);

      expect(response.body).toHaveProperty('statusCode', 401);
    });

    it('should return 401 when only password is provided', async () => {
      const correlationId = uuidv4();
      const response = await getHttpServer(app)
        .get('/protected')
        .set('X-Correlation-ID', correlationId)
        .auth('', TEST_CREDENTIALS.PASSWORD)
        .expect(401);

      expect(response.body).toHaveProperty('statusCode', 401);
    });
  });

  describe('Unprotected endpoint /', () => {
    it('should return 400 when no correlation ID is provided', async () => {
      const response = await getHttpServer(app).get('/').expect(400);

      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty(
        'message',
        'X-Correlation-ID header is required',
      );
    });

    it('should return 200 without authentication but with correlation ID', async () => {
      const correlationId = uuidv4();
      const response = await getHttpServer(app)
        .get('/')
        .set('X-Correlation-ID', correlationId)
        .expect(200);

      expect(response.text).toBe('Hello World!');
      expect(response.headers).toHaveProperty(
        'x-correlation-id',
        correlationId,
      );
    });

    it('should return 200 even with authentication and correlation ID', async () => {
      const correlationId = uuidv4();
      const response = await getHttpServer(app)
        .get('/')
        .set('X-Correlation-ID', correlationId)
        .auth(TEST_CREDENTIALS.USERNAME, TEST_CREDENTIALS.PASSWORD)
        .expect(200);

      expect(response.text).toBe('Hello World!');
      expect(response.headers).toHaveProperty(
        'x-correlation-id',
        correlationId,
      );
    });
  });

  describe('Health endpoint /health', () => {
    it('should return 400 when no correlation ID is provided', async () => {
      const response = await getHttpServer(app).get('/health').expect(400);

      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty(
        'message',
        'X-Correlation-ID header is required',
      );
    });

    it('should return 200 without authentication but with correlation ID', async () => {
      const correlationId = uuidv4();
      const response = await getHttpServer(app)
        .get('/health')
        .set('X-Correlation-ID', correlationId)
        .expect(200);

      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('version');
      expect(response.headers).toHaveProperty(
        'x-correlation-id',
        correlationId,
      );
    });
  });
});
