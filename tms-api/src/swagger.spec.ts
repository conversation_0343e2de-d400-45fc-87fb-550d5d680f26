/**
 * Swagger Documentation Tests
 *
 * These tests verify that the Swagger/OpenAPI documentation is properly configured
 * and accessible at the expected endpoint.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import request from 'supertest';
import { ValidationPipe } from './pipes/validation.pipe';
import { GlobalExceptionFilter } from './filters/global-exception.filter';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { QuizController } from './quiz/quiz.controller';
import { QuizService } from './quiz/quiz.service';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ThrottlerModule } from '@nestjs/throttler';
import { AuthModule } from './auth/auth.module';
import {
  ErrorResponseSchema,
  ValidationErrorDetailSchema,
  ValidationErrorResponseSchema,
  AuthenticationErrorResponseSchema,
  NotFoundErrorResponseSchema,
  FileTooLargeErrorResponseSchema,
  InternalServerErrorResponseSchema,
} from './schemas';

describe('Swagger Documentation', () => {
  let app: INestApplication;
  let module: TestingModule;

  beforeAll(async () => {
    // Create a minimal test module without database dependencies
    // This focuses only on testing Swagger documentation functionality
    const mockQuizService = {
      getQuizWorkedSolutions: jest.fn(),
      uploadQuizWorkedSolutions: jest.fn(),
      updateQuiz: jest.fn(),
      deleteQuiz: jest.fn(),
    };

    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env',
        }),
        ThrottlerModule.forRoot([
          {
            name: 'default',
            ttl: 60000,
            limit: 1000,
          },
        ]),
        AuthModule,
      ],
      controllers: [AppController, QuizController],
      providers: [
        AppService,
        {
          provide: QuizService,
          useValue: mockQuizService,
        },
      ],
    }).compile();

    app = module.createNestApplication();

    // Apply the same configuration as main.ts
    app.useGlobalPipes(new ValidationPipe());
    app.useGlobalFilters(new GlobalExceptionFilter());
    app.useGlobalInterceptors(new LoggingInterceptor());

    // Configure Swagger/OpenAPI documentation (same as main.ts)
    const config = new DocumentBuilder()
      .setTitle('Teaching Material System (TMS) REST API')
      .setDescription(
        'A centralized service for storing, retrieving, updating, and deleting F2F paperless marking worked solution quiz materials',
      )
      .setVersion('0.0.1')
      .addBasicAuth(
        {
          type: 'http',
          scheme: 'basic',
          description: 'Basic Authentication using username and password',
        },
        'basic',
      )
      .addApiKey(
        {
          type: 'apiKey',
          name: 'X-Correlation-ID',
          in: 'header',
          description: 'Correlation ID for request tracing (UUID format)',
        },
        'correlation-id',
      )
      .build();

    const document = SwaggerModule.createDocument(app, config, {
      extraModels: [
        // Include error response schemas for documentation
        ErrorResponseSchema,
        ValidationErrorDetailSchema,
        ValidationErrorResponseSchema,
        AuthenticationErrorResponseSchema,
        NotFoundErrorResponseSchema,
        FileTooLargeErrorResponseSchema,
        InternalServerErrorResponseSchema,
      ],
    });
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
      },
    });

    await app.init();
  });

  afterAll(async () => {
    try {
      if (app) {
        await app.close();
      }
      if (module) {
        await module.close();
      }
    } catch (error) {
      console.error('Error during test cleanup:', error);
    }
  });

  describe('Swagger UI Endpoint', () => {
    it('should serve Swagger UI at /api/docs', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/docs')
        .expect(200);

      // Verify that the response contains Swagger UI HTML
      expect(response.text).toContain('swagger-ui');
      expect(response.text).toContain('Swagger UI');
      expect(response.text).toContain('swagger-ui-bundle.js');
      expect(response.headers['content-type']).toMatch(/text\/html/);
    });

    it('should serve Swagger UI redirect at /api/docs/', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/docs/')
        .expect(200);

      // Verify that the response contains Swagger UI HTML
      expect(response.text).toContain('swagger-ui');
      expect(response.text).toContain('Swagger UI');
      expect(response.text).toContain('swagger-ui-bundle.js');
    });
  });

  describe('OpenAPI JSON Specification', () => {
    it('should serve OpenAPI JSON at /api/docs-json', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/docs-json')
        .expect(200);

      const openApiSpec = response.body;

      // Verify basic OpenAPI structure
      expect(openApiSpec).toHaveProperty('openapi');
      expect(openApiSpec).toHaveProperty('info');
      expect(openApiSpec).toHaveProperty('paths');
      expect(openApiSpec).toHaveProperty('components');

      // Verify API metadata from Design Doc
      expect(openApiSpec.info.title).toBe(
        'Teaching Material System (TMS) REST API',
      );
      expect(openApiSpec.info.description).toBe(
        'A centralized service for storing, retrieving, updating, and deleting F2F paperless marking worked solution quiz materials',
      );
      expect(openApiSpec.info.version).toBe('0.0.1');

      // Verify security schemes are configured
      expect(openApiSpec.components.securitySchemes).toHaveProperty('basic');
      expect(openApiSpec.components.securitySchemes).toHaveProperty(
        'correlation-id',
      );

      // Verify Basic Auth configuration
      expect(openApiSpec.components.securitySchemes.basic.type).toBe('http');
      expect(openApiSpec.components.securitySchemes.basic.scheme).toBe('basic');

      // Verify X-Correlation-ID header configuration
      expect(
        openApiSpec.components.securitySchemes['correlation-id'].type,
      ).toBe('apiKey');
      expect(
        openApiSpec.components.securitySchemes['correlation-id'].name,
      ).toBe('X-Correlation-ID');
      expect(openApiSpec.components.securitySchemes['correlation-id'].in).toBe(
        'header',
      );
    });

    it('should include quiz endpoints in the specification', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/docs-json')
        .expect(200);

      const openApiSpec = response.body;

      // Verify that quiz endpoints are documented
      expect(openApiSpec.paths).toHaveProperty(
        '/quiz/f2f/paperless-marking-worked-solutions',
      );

      const quizPath =
        openApiSpec.paths['/quiz/f2f/paperless-marking-worked-solutions'];

      // Verify GET endpoint exists
      expect(quizPath).toHaveProperty('get');

      // Verify POST endpoint exists
      expect(quizPath).toHaveProperty('post');
    });
  });

  describe('API Documentation Content', () => {
    it('should include proper API metadata in the documentation', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/docs-json')
        .expect(200);

      const openApiSpec = response.body;

      // Verify OpenAPI version
      expect(openApiSpec.openapi).toMatch(/^3\./);

      // Verify comprehensive info section
      expect(openApiSpec.info).toMatchObject({
        title: 'Teaching Material System (TMS) REST API',
        description:
          'A centralized service for storing, retrieving, updating, and deleting F2F paperless marking worked solution quiz materials',
        version: '0.0.1',
      });

      // Verify security schemes match Design Doc requirements
      expect(openApiSpec.components.securitySchemes.basic).toMatchObject({
        type: 'http',
        scheme: 'basic',
        description: 'Basic Authentication using username and password',
      });

      expect(
        openApiSpec.components.securitySchemes['correlation-id'],
      ).toMatchObject({
        type: 'apiKey',
        name: 'X-Correlation-ID',
        in: 'header',
        description: 'Correlation ID for request tracing (UUID format)',
      });
    });

    it('should include error response schemas in the specification', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/docs-json')
        .expect(200);

      const openApiSpec = response.body;

      // Verify that error response schemas are included
      expect(openApiSpec.components.schemas).toHaveProperty(
        'ErrorResponseSchema',
      );
      expect(openApiSpec.components.schemas).toHaveProperty(
        'ValidationErrorResponseSchema',
      );
      expect(openApiSpec.components.schemas).toHaveProperty(
        'AuthenticationErrorResponseSchema',
      );
      expect(openApiSpec.components.schemas).toHaveProperty(
        'NotFoundErrorResponseSchema',
      );
      expect(openApiSpec.components.schemas).toHaveProperty(
        'FileTooLargeErrorResponseSchema',
      );
      expect(openApiSpec.components.schemas).toHaveProperty(
        'InternalServerErrorResponseSchema',
      );

      // Verify error response schema structure
      const errorSchema = openApiSpec.components.schemas.ErrorResponseSchema;
      expect(errorSchema.properties).toHaveProperty('statusCode');
      expect(errorSchema.properties).toHaveProperty('message');
      expect(errorSchema.properties).toHaveProperty('details');
      expect(errorSchema.properties).toHaveProperty('correlationId');
      expect(errorSchema.properties).toHaveProperty('timestamp');
      expect(errorSchema.properties).toHaveProperty('path');
    });

    it('should include authentication documentation in protected endpoints', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/docs-json')
        .expect(200);

      const openApiSpec = response.body;

      // Verify that protected endpoints have security requirements
      const protectedEndpoint = openApiSpec.paths['/protected'];
      expect(protectedEndpoint).toBeDefined();
      expect(protectedEndpoint.get).toHaveProperty('security');

      // Verify that quiz endpoints have security requirements
      const quizEndpoint =
        openApiSpec.paths['/quiz/f2f/paperless-marking-worked-solutions'];
      expect(quizEndpoint).toBeDefined();
      expect(quizEndpoint.get).toHaveProperty('security');
      expect(quizEndpoint.post).toHaveProperty('security');
    });
  });
});
