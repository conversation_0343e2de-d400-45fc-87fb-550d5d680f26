/**
 * Database Module Tests
 *
 * These tests use a real PostgreSQL database connection instead of mocks,
 * following our principle of "never mock components unless absolutely necessary".
 *
 * The tests connect to a dedicated test database instance (configured in .env)
 * to provide more realistic testing of database interactions.
 */

import { TestingModule } from '@nestjs/testing';
import { DatabaseModule } from './database.module';
import { DatabaseService } from './database.service';
import { DataSource } from 'typeorm';
import { cleanupDatabaseWithCallback } from '../../test/utils/database-cleanup';
import { createTestModule } from '../../test/utils/test-module-factory';

describe('DatabaseModule', () => {
  let module: TestingModule;
  let dataSource: DataSource;

  // Set a longer timeout for all tests in this suite (60 seconds)
  beforeAll(() => {
    // Set timeout before fake timers to ensure it takes effect
    jest.setTimeout(60000);

    // Use real timers for database connection operations
    // Fake timers can interfere with async database operations
    jest.useRealTimers();
  });

  // Use the shared cleanup utility for consistent cleanup behavior
  afterAll((done) => {
    cleanupDatabaseWithCallback(dataSource, module, done);
  }, 60000); // 60 second timeout

  // Use the shared cleanup utility for consistent cleanup behavior
  afterEach((done) => {
    cleanupDatabaseWithCallback(dataSource, module, done);
  }, 60000); // 60 second timeout

  it('should compile the module and connect to the database', (done) => {
    // Create a module with a real database connection using the shared factory
    createTestModule({
      imports: [DatabaseModule],
      isGlobalConfig: true,
      enableLogging: false,
    })
      .then((compiledModule) => {
        module = compiledModule;
        expect(module).toBeDefined();

        const databaseService = module.get(DatabaseService);
        expect(databaseService).toBeInstanceOf(DatabaseService);

        dataSource = module.get(DataSource);
        expect(dataSource).toBeDefined();
        expect(dataSource.isInitialized).toBe(true);

        // Verify database connection is healthy
        return databaseService.isHealthy();
      })
      .then((isHealthy) => {
        expect(isHealthy).toBe(true);
        done();
      })
      .catch((error: Error) => {
        console.error('Test failed with error:', error);
        done.fail(error);
      });
  });
});
