/**
 * Database Service Tests
 *
 * These tests use a real PostgreSQL database connection instead of mocks,
 * following our principle of "never mock components unless absolutely necessary".
 *
 * The tests connect to a dedicated test database instance (configured in .env)
 * to provide more realistic testing of database interactions.
 */

import { TestingModule } from '@nestjs/testing';
import { DatabaseService } from './database.service';
import { DataSource } from 'typeorm';
import { cleanupDatabaseWithCallback } from '../../test/utils/database-cleanup';
import { createTestModule } from '../../test/utils/test-module-factory';

// Interface for accessing private properties in tests
interface DatabaseServiceWithPrivates {
  dataSource: DataSource | null;
}

describe('DatabaseService', () => {
  let service: DatabaseService;
  let dataSource: DataSource;
  let module: TestingModule;

  // Set a longer timeout and use real timers for database operations
  beforeAll(() => {
    // Set a longer timeout for all tests in this suite (60 seconds)
    jest.setTimeout(60000);
    // Use real timers for database connection operations
    // Fake timers can interfere with async database operations
    jest.useRealTimers();
  });

  // Use the shared cleanup utility for consistent cleanup behavior
  afterAll((done) => {
    cleanupDatabaseWithCallback(dataSource, module, done);
  }, 60000); // 60 second timeout

  beforeEach(async () => {
    // Create a module with a real database connection using the shared factory
    module = await createTestModule({
      providers: [DatabaseService],
      enableLogging: false,
      dropSchema: false,
    });

    service = module.get<DatabaseService>(DatabaseService);
    dataSource = module.get<DataSource>(DataSource);
  });

  // Use the shared cleanup utility for consistent cleanup behavior
  afterEach((done) => {
    cleanupDatabaseWithCallback(dataSource, module, done);
  }, 60000); // 60 second timeout

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return true when database is healthy', async () => {
    const result = await service.isHealthy();
    expect(result).toBe(true);
  });

  it('should return connected status when database is healthy', async () => {
    const status = await service.getStatus();

    expect(status.isConnected).toBe(true);
    expect(status.message).toBe('Database connection is healthy');
  });

  // Test for database connection failure can be simulated by temporarily
  // modifying the dataSource object, but we'll use a more controlled approach
  it('should return false when database is not initialized', async () => {
    // Save the original isInitialized property
    const originalIsInitialized = Object.getOwnPropertyDescriptor(
      Object.getPrototypeOf(dataSource),
      'isInitialized',
    );

    // Temporarily override the isInitialized property
    Object.defineProperty(dataSource, 'isInitialized', {
      get: () => false,
      configurable: true,
    });

    const result = await service.isHealthy();
    expect(result).toBe(false);

    // Restore the original property
    if (originalIsInitialized) {
      Object.defineProperty(dataSource, 'isInitialized', originalIsInitialized);
    }
  });

  it('should return not connected status when database is not healthy', async () => {
    // Save the original isInitialized property
    const originalIsInitialized = Object.getOwnPropertyDescriptor(
      Object.getPrototypeOf(dataSource),
      'isInitialized',
    );

    // Temporarily override the isInitialized property
    Object.defineProperty(dataSource, 'isInitialized', {
      get: () => false,
      configurable: true,
    });

    const status = await service.getStatus();

    expect(status.isConnected).toBe(false);
    expect(status.message).toBe('Database connection is not established');

    // Restore the original property
    if (originalIsInitialized) {
      Object.defineProperty(dataSource, 'isInitialized', originalIsInitialized);
    }
  });

  it('should return false when a real error occurs during database health check', (done) => {
    console.log('---------------------------');
    console.log(
      'EXPECTED ERROR: The following database error is intentional for testing error handling',
    );
    console.log('---------------------------');

    // Create a real error scenario by setting dataSource to null
    // This will cause an error when trying to access isInitialized
    const serviceWithPrivates =
      service as unknown as DatabaseServiceWithPrivates;
    const originalDataSource = serviceWithPrivates.dataSource;
    serviceWithPrivates.dataSource = null;

    // This should now trigger the real catch block in isHealthy()
    void service.isHealthy().then((result) => {
      // Verify the method returns false when an error occurs
      expect(result).toBe(false);

      // Restore the original dataSource
      serviceWithPrivates.dataSource = originalDataSource;

      done();
    });
  });
});
