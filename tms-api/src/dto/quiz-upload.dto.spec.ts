/**
 * Quiz Upload DTO Tests
 *
 * These tests verify the validation rules for the QuizUploadDto class.
 * They test all validation scenarios for the POST endpoint parameters.
 */

import { validate, ValidationError } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { QuizUploadDto } from './quiz-upload.dto';
import { WeekType } from '../entities/material.entity';

interface TestQuizUploadData {
  year: number;
  term: number;
  week: number;
  weekType: WeekType;
  teachingProgram?: string;
}

describe('QuizUploadDto', () => {
  /**
   * Helper function to validate a DTO and return validation errors
   */
  async function validateDto(dto: object): Promise<string[]> {
    const validationErrors: ValidationError[] = await validate(dto);
    return validationErrors.flatMap((error: ValidationError) =>
      error.constraints ? Object.values(error.constraints) : [],
    );
  }

  /**
   * Helper function to create a valid DTO with default values
   */
  function createValidDto(): TestQuizUploadData {
    return {
      year: 2025,
      term: 2,
      week: 4,
      weekType: WeekType.NORMAL,
      teachingProgram: 'St George Girls',
    };
  }

  describe('Valid inputs', () => {
    it('should pass validation with all required fields', async () => {
      const dto = plainToClass(QuizUploadDto, {
        year: 2025,
        term: 2,
        week: 4,
        weekType: WeekType.NORMAL,
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation with optional teachingProgram included', async () => {
      const dto = plainToClass(QuizUploadDto, createValidDto());

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation with teachingProgram omitted', async () => {
      const validData = createValidDto();
      delete validData.teachingProgram;

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should handle string numbers correctly with transformation', async () => {
      const dto = plainToClass(QuizUploadDto, {
        year: '2025',
        term: '2',
        week: '4',
        weekType: WeekType.NORMAL,
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
      expect(dto.year).toBe(2025);
      expect(dto.term).toBe(2);
      expect(dto.week).toBe(4);
    });

    it('should trim whitespace from teachingProgram', async () => {
      const dto = plainToClass(QuizUploadDto, {
        year: 2025,
        term: 2,
        week: 4,
        weekType: WeekType.NORMAL,
        teachingProgram: '  St George Girls  ',
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
      expect(dto.teachingProgram).toBe('St George Girls');
    });
  });

  describe('Year validation', () => {
    it('should reject non-integer year', async () => {
      const validData = createValidDto();
      (validData as unknown as Record<string, unknown>).year = 'not-a-number';

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Year must be an integer');
    });

    it('should reject year below minimum (2000)', async () => {
      const validData = createValidDto();
      validData.year = 1999;

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Year must be at least 2000');
    });

    it('should reject year above maximum (2100)', async () => {
      const validData = createValidDto();
      validData.year = 2101;

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Year must be at most 2100');
    });

    it('should accept boundary values for year', async () => {
      // Test minimum boundary
      const minDto = plainToClass(QuizUploadDto, {
        ...createValidDto(),
        year: 2000,
      });
      const minErrors = await validateDto(minDto);
      expect(minErrors.filter((e) => e.includes('Year'))).toHaveLength(0);

      // Test maximum boundary
      const maxDto = plainToClass(QuizUploadDto, {
        ...createValidDto(),
        year: 2100,
      });
      const maxErrors = await validateDto(maxDto);
      expect(maxErrors.filter((e) => e.includes('Year'))).toHaveLength(0);
    });
  });

  describe('Term validation', () => {
    it('should reject non-integer term', async () => {
      const validData = createValidDto();
      (validData as unknown as Record<string, unknown>).term = 'not-a-number';

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Term must be an integer');
    });

    it('should reject term below minimum (1)', async () => {
      const validData = createValidDto();
      validData.term = 0;

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Term must be at least 1');
    });

    it('should reject term above maximum (4)', async () => {
      const validData = createValidDto();
      validData.term = 5;

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Term must be at most 4');
    });

    it('should accept boundary values for term', async () => {
      // Test minimum boundary
      const minDto = plainToClass(QuizUploadDto, {
        ...createValidDto(),
        term: 1,
      });
      const minErrors = await validateDto(minDto);
      expect(minErrors.filter((e) => e.includes('Term'))).toHaveLength(0);

      // Test maximum boundary
      const maxDto = plainToClass(QuizUploadDto, {
        ...createValidDto(),
        term: 4,
      });
      const maxErrors = await validateDto(maxDto);
      expect(maxErrors.filter((e) => e.includes('Term'))).toHaveLength(0);
    });
  });

  describe('Week validation', () => {
    it('should reject non-integer week', async () => {
      const validData = createValidDto();
      (validData as unknown as Record<string, unknown>).week = 'not-a-number';

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Week must be an integer');
    });

    it('should reject week below minimum (1)', async () => {
      const validData = createValidDto();
      validData.week = 0;

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Week must be at least 1');
    });

    it('should reject week above maximum (52)', async () => {
      const validData = createValidDto();
      validData.week = 53;

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain('Week must be at most 52');
    });

    it('should accept boundary values for week', async () => {
      // Test minimum boundary
      const minDto = plainToClass(QuizUploadDto, {
        ...createValidDto(),
        week: 1,
      });
      const minErrors = await validateDto(minDto);
      expect(minErrors.filter((e) => e.includes('Week'))).toHaveLength(0);

      // Test maximum boundary
      const maxDto = plainToClass(QuizUploadDto, {
        ...createValidDto(),
        week: 52,
      });
      const maxErrors = await validateDto(maxDto);
      expect(maxErrors.filter((e) => e.includes('Week'))).toHaveLength(0);
    });
  });

  describe('WeekType validation', () => {
    it('should accept valid week types', async () => {
      const normalDto = plainToClass(QuizUploadDto, {
        ...createValidDto(),
        weekType: WeekType.NORMAL,
      });
      const normalErrors = await validateDto(normalDto);
      expect(normalErrors.filter((e) => e.includes('Week type'))).toHaveLength(
        0,
      );

      const holidayDto = plainToClass(QuizUploadDto, {
        ...createValidDto(),
        weekType: WeekType.HOLIDAY,
      });
      const holidayErrors = await validateDto(holidayDto);
      expect(holidayErrors.filter((e) => e.includes('Week type'))).toHaveLength(
        0,
      );
    });

    it('should reject invalid week type', async () => {
      const validData = createValidDto();
      (validData as unknown as Record<string, unknown>).weekType = 'invalid';

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain(
        'Week type must be either "normal" or "holiday"',
      );
    });
  });

  describe('TeachingProgram validation', () => {
    it('should accept undefined teachingProgram', async () => {
      const dto = plainToClass(QuizUploadDto, {
        year: 2025,
        term: 2,
        week: 4,
        weekType: WeekType.NORMAL,
        // teachingProgram omitted
      });

      const errors = await validateDto(dto);
      expect(errors).toHaveLength(0);
    });

    it('should accept empty teachingProgram', async () => {
      const validData = createValidDto();
      validData.teachingProgram = '';

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors.filter((e) => e.includes('Teaching program'))).toHaveLength(
        0,
      );
    });

    // === EDGE CASE TESTS FROM TRAINING DATA ANALYSIS ===

    it('should handle teaching programs with special characters', async () => {
      // Arrange - Based on actual teaching program patterns that might appear
      const specialCharacterPrograms = [
        'St George Girls High School',
        'Sydney Boys & Girls High',
        "North Sydney Girls' School",
        'Parramatta High (2024)',
        'Selective School - Advanced',
        'International School (IB)',
      ];

      for (const program of specialCharacterPrograms) {
        const validData = createValidDto();
        validData.teachingProgram = program;

        const dto = plainToClass(QuizUploadDto, validData);
        const errors = await validateDto(dto);

        // Assert
        expect(errors).toHaveLength(0);
        expect(dto.teachingProgram).toBe(program);
      }
    });

    it('should handle very long teaching program names', async () => {
      // Arrange - Test boundary condition for long names
      const longProgramName = 'A'.repeat(200); // Very long program name
      const validData = createValidDto();
      validData.teachingProgram = longProgramName;

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);

      // Assert - Should accept long names (no length restriction in current implementation)
      expect(errors).toHaveLength(0);
      expect(dto.teachingProgram).toBe(longProgramName);
    });
  });

  // === NEW EDGE CASE TESTS BASED ON TRAINING DATA PATTERNS ===

  describe('Edge cases from training data analysis', () => {
    it('should handle boundary year values', async () => {
      // Arrange - Test years that might appear in real usage
      const boundaryYears = [2020, 2025, 2030, 2050]; // Past, current, near future, far future

      for (const year of boundaryYears) {
        const dto = plainToClass(QuizUploadDto, {
          year: year,
          term: 2,
          week: 4,
          weekType: WeekType.NORMAL,
        });

        const errors = await validateDto(dto);

        // Assert
        expect(errors).toHaveLength(0);
        expect(dto.year).toBe(year);
      }
    });

    it('should handle all valid term values', async () => {
      // Arrange - Test all possible term values
      const validTerms = [1, 2, 3, 4];

      for (const term of validTerms) {
        const dto = plainToClass(QuizUploadDto, {
          year: 2025,
          term: term,
          week: 4,
          weekType: WeekType.NORMAL,
        });

        const errors = await validateDto(dto);

        // Assert
        expect(errors).toHaveLength(0);
        expect(dto.term).toBe(term);
      }
    });

    it('should handle boundary week values', async () => {
      // Arrange - Test week boundary conditions
      const boundaryWeeks = [1, 10, 20, 52]; // Start, middle, high, maximum

      for (const week of boundaryWeeks) {
        const dto = plainToClass(QuizUploadDto, {
          year: 2025,
          term: 2,
          week: week,
          weekType: WeekType.NORMAL,
        });

        const errors = await validateDto(dto);

        // Assert
        expect(errors).toHaveLength(0);
        expect(dto.week).toBe(week);
      }
    });

    it('should handle all weekType enum values', async () => {
      // Arrange - Test all possible weekType values
      const allWeekTypes = Object.values(WeekType);

      for (const weekType of allWeekTypes) {
        const dto = plainToClass(QuizUploadDto, {
          year: 2025,
          term: 2,
          week: 4,
          weekType: weekType,
        });

        const errors = await validateDto(dto);

        // Assert
        expect(errors).toHaveLength(0);
        expect(dto.weekType).toBe(weekType);
      }
    });

    it('should handle realistic combinations from training data patterns', async () => {
      // Arrange - Test combinations that mirror actual training data patterns
      const realisticCombinations = [
        {
          year: 2025,
          term: 2,
          week: 4,
          weekType: WeekType.NORMAL,
          teachingProgram: 'St George Girls',
        },
        {
          year: 2025,
          term: 4,
          week: 20,
          weekType: WeekType.NORMAL,
          teachingProgram: 'Comprehensive Test',
        },
        {
          year: 2025,
          term: 1,
          week: 1,
          weekType: WeekType.HOLIDAY,
          teachingProgram: undefined,
        },
        {
          year: 2024,
          term: 4,
          week: 52,
          weekType: WeekType.NORMAL,
          teachingProgram: 'Year End Assessment',
        },
      ];

      for (const combination of realisticCombinations) {
        const dto = plainToClass(QuizUploadDto, combination);
        const errors = await validateDto(dto);

        // Assert
        expect(errors).toHaveLength(0);
        expect(dto.year).toBe(combination.year);
        expect(dto.term).toBe(combination.term);
        expect(dto.week).toBe(combination.week);
        expect(dto.weekType).toBe(combination.weekType);
        if (combination.teachingProgram !== undefined) {
          expect(dto.teachingProgram).toBe(combination.teachingProgram);
        }
      }
    });

    it('should reject teachingProgram longer than 255 characters', async () => {
      const validData = createValidDto();
      validData.teachingProgram = 'a'.repeat(256);

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors).toContain(
        'Teaching program must be at most 255 characters',
      );
    });

    it('should accept teachingProgram at boundary length (255 characters)', async () => {
      const validData = createValidDto();
      validData.teachingProgram = 'a'.repeat(255);

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors.filter((e) => e.includes('Teaching program'))).toHaveLength(
        0,
      );
    });
  });

  describe('Missing required fields', () => {
    it('should reject missing year', async () => {
      const validData = createValidDto();
      delete (validData as Partial<TestQuizUploadData>).year;

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors.some((e) => e.includes('Year'))).toBe(true);
    });

    it('should reject missing term', async () => {
      const validData = createValidDto();
      delete (validData as Partial<TestQuizUploadData>).term;

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors.some((e) => e.includes('Term'))).toBe(true);
    });

    it('should reject missing week', async () => {
      const validData = createValidDto();
      delete (validData as Partial<TestQuizUploadData>).week;

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors.some((e) => e.includes('Week'))).toBe(true);
    });

    it('should reject missing weekType', async () => {
      const validData = createValidDto();
      delete (validData as Partial<TestQuizUploadData>).weekType;

      const dto = plainToClass(QuizUploadDto, validData);
      const errors = await validateDto(dto);
      expect(errors.some((e) => e.includes('Week type'))).toBe(true);
    });
  });
});
