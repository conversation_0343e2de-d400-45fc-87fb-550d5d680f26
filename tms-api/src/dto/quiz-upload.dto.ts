/**
 * Quiz Upload DTO
 *
 * This DTO defines the validation rules for query parameters used in the
 * POST /quiz/f2f/paperless-marking-worked-solutions endpoint.
 *
 * Based on PRD requirements:
 * - year (integer, Required, e.g., 2025)
 * - term (integer, Required, e.g., 2)
 * - week (integer, Required, e.g., 4)
 * - weekType (string, Required, e.g., "normal" or "holiday")
 * - teachingProgram (string, Optional, e.g., "St George Girls")
 *
 * Note: Most metadata (grade, subject, course, classLevel, color, topic)
 * is extracted from the uploaded ZIP file's LessonMetadata.json
 */

import {
  IsInt,
  IsString,
  IsEnum,
  IsOptional,
  Min,
  Max,
  Length,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { WeekType } from '../entities/material.entity';

export class QuizUploadDto {
  /**
   * Year (2000-2100)
   */
  @ApiProperty({
    description: 'Academic year',
    type: 'integer',
    minimum: 2000,
    maximum: 2100,
    example: 2025,
  })
  @Type(() => Number)
  @IsInt({ message: 'Year must be an integer' })
  @Min(2000, { message: 'Year must be at least 2000' })
  @Max(2100, { message: 'Year must be at most 2100' })
  year!: number;

  /**
   * Term (1-4)
   */
  @ApiProperty({
    description: 'Academic term (1-4)',
    type: 'integer',
    minimum: 1,
    maximum: 4,
    example: 2,
  })
  @Type(() => Number)
  @IsInt({ message: 'Term must be an integer' })
  @Min(1, { message: 'Term must be at least 1' })
  @Max(4, { message: 'Term must be at most 4' })
  term!: number;

  /**
   * Week (1-52)
   */
  @ApiProperty({
    description: 'Week number (1-52)',
    type: 'integer',
    minimum: 1,
    maximum: 52,
    example: 4,
  })
  @Type(() => Number)
  @IsInt({ message: 'Week must be an integer' })
  @Min(1, { message: 'Week must be at least 1' })
  @Max(52, { message: 'Week must be at most 52' })
  week!: number;

  /**
   * Week type (normal or holiday)
   */
  @ApiProperty({
    description: 'Week type',
    enum: WeekType,
    example: 'normal',
  })
  @IsEnum(WeekType, {
    message: 'Week type must be either "normal" or "holiday"',
  })
  weekType!: WeekType;

  /**
   * Teaching program (optional)
   */
  @ApiPropertyOptional({
    description: 'Teaching program name (optional)',
    type: 'string',
    maxLength: 255,
    example: 'St George Girls',
  })
  @IsOptional()
  @IsString({ message: 'Teaching program must be a string' })
  @Length(0, 255, {
    message: 'Teaching program must be at most 255 characters',
  })
  @Transform(({ value }: { value: unknown }) =>
    typeof value === 'string' ? value.trim() : value || '',
  )
  teachingProgram?: string;
}
