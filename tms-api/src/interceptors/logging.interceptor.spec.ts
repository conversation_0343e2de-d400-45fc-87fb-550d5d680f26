/**
 * Logging Interceptor Unit Tests
 *
 * Tests for the LoggingInterceptor functionality.
 */

import { Execution<PERSON>ontext, <PERSON><PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { of, throwError } from 'rxjs';
import { LoggingInterceptor } from './logging.interceptor';

// Type definitions for test data
interface LoggedData {
  correlationId: string;
  method: string;
  url: string;
  statusCode?: number;
  responseTime?: number;
  userAgent?: string;
  ip?: string;
  contentLength?: number;
  timestamp: string;
  type: string;
  error?: string;
}

interface MockLogCall {
  0: string; // log message
  1?: string; // JSON data
}

describe('LoggingInterceptor', () => {
  let interceptor: LoggingInterceptor;
  let mockExecutionContext: Partial<ExecutionContext>;
  let mockCallHandler: Partial<CallHandler>;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let logSpy: jest.SpyInstance;
  let warnSpy: jest.SpyInstance;
  let errorSpy: jest.SpyInstance;

  beforeEach(() => {
    interceptor = new LoggingInterceptor();

    // Mock request object
    mockRequest = {
      method: 'GET',
      url: '/test',
      correlationId: 'test-correlation-id',
      ip: '127.0.0.1',
      connection: { remoteAddress: '127.0.0.1' } as any,
      get: jest.fn((header: string) => {
        if (header === 'User-Agent') return 'test-user-agent';
        if (header === 'set-cookie') return ['cookie1', 'cookie2'];
        return undefined;
      }) as any,
    };

    // Mock response object
    mockResponse = {
      statusCode: 200,
    };

    // Mock execution context
    mockExecutionContext = {
      switchToHttp: jest.fn().mockReturnValue({
        getRequest: () => mockRequest,
        getResponse: () => mockResponse,
      }),
    };

    // Mock call handler
    mockCallHandler = {
      handle: jest.fn(),
    };

    // Spy on logger methods
    logSpy = jest
      .spyOn(Logger.prototype, 'log')
      .mockImplementation(() => undefined);
    warnSpy = jest
      .spyOn(Logger.prototype, 'warn')
      .mockImplementation(() => undefined);
    errorSpy = jest
      .spyOn(Logger.prototype, 'error')
      .mockImplementation(() => undefined);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should be defined', () => {
    expect(interceptor).toBeDefined();
  });

  describe('successful request handling', () => {
    it('should log request and successful response', (done) => {
      const responseData = { message: 'success' };
      (mockCallHandler.handle as jest.Mock).mockReturnValue(of(responseData));

      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      result.subscribe({
        next: (data) => {
          expect(data).toEqual(responseData);

          // Verify request logging
          expect(logSpy).toHaveBeenCalledWith(
            'GET /test - Request received',
            expect.stringContaining('"correlationId":"test-correlation-id"'),
          );

          // Verify response logging
          expect(logSpy).toHaveBeenCalledWith(
            expect.stringMatching(/GET \/test - 200 \(\d+ms\)/),
            expect.stringContaining('"correlationId":"test-correlation-id"'),
          );

          done();
        },
        error: done,
      });
    });

    it('should calculate response time correctly', (done) => {
      const responseData = { message: 'success' };
      (mockCallHandler.handle as jest.Mock).mockReturnValue(of(responseData));

      const startTime = Date.now();
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      result.subscribe({
        next: () => {
          const endTime = Date.now();
          const expectedMinTime = endTime - startTime;

          // Check that response time was logged
          const logCalls = logSpy.mock.calls as MockLogCall[];
          const responseLogCall = logCalls.find((call) =>
            call[0].includes('200'),
          );

          expect(responseLogCall).toBeDefined();
          expect(responseLogCall?.[1]).toContain('"responseTime":');

          // Parse the logged data to verify response time
          const loggedData = JSON.parse(
            responseLogCall?.[1] ?? '{}',
          ) as LoggedData;
          expect(loggedData.responseTime).toBeGreaterThanOrEqual(0);
          expect(loggedData.responseTime).toBeLessThanOrEqual(
            expectedMinTime + 100,
          ); // Allow some tolerance

          done();
        },
        error: done,
      });
    });

    it('should include correlation ID in logs', (done) => {
      const responseData = { message: 'success' };
      (mockCallHandler.handle as jest.Mock).mockReturnValue(of(responseData));

      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      result.subscribe({
        next: () => {
          // Check request log
          const logCalls = logSpy.mock.calls as MockLogCall[];
          const requestLogCall = logCalls[0];
          expect(requestLogCall?.[1]).toContain(
            '"correlationId":"test-correlation-id"',
          );

          // Check response log
          const responseLogCall = logCalls[1];
          expect(responseLogCall?.[1]).toContain(
            '"correlationId":"test-correlation-id"',
          );

          done();
        },
        error: done,
      });
    });

    it('should handle missing correlation ID gracefully', (done) => {
      // Remove correlation ID from request
      delete (mockRequest as Partial<typeof mockRequest>).correlationId;

      const responseData = { message: 'success' };
      (mockCallHandler.handle as jest.Mock).mockReturnValue(of(responseData));

      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      result.subscribe({
        next: () => {
          // Check that 'N/A' is used when correlation ID is missing
          const logCalls = logSpy.mock.calls as MockLogCall[];
          const requestLogCall = logCalls[0];
          expect(requestLogCall?.[1]).toContain('"correlationId":"N/A"');

          done();
        },
        error: done,
      });
    });
  });

  describe('error handling', () => {
    it('should log error responses', (done) => {
      const error = new Error('Test error');
      (mockCallHandler.handle as jest.Mock).mockReturnValue(throwError(error));

      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      result.subscribe({
        next: () => {
          done(new Error('Should not reach next handler'));
        },
        error: (err) => {
          expect(err).toBe(error);

          // Verify request logging
          expect(logSpy).toHaveBeenCalledWith(
            'GET /test - Request received',
            expect.stringContaining('"correlationId":"test-correlation-id"'),
          );

          // Verify error logging
          expect(errorSpy).toHaveBeenCalledWith(
            expect.stringMatching(/GET \/test - \d+ \(\d+ms\) - Test error/),
            expect.stringContaining('"correlationId":"test-correlation-id"'),
          );

          done();
        },
      });
    });

    it('should handle errors without message', (done) => {
      const error = { someProperty: 'value' }; // Error without message
      (mockCallHandler.handle as jest.Mock).mockReturnValue(throwError(error));

      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      result.subscribe({
        next: () => {
          done(new Error('Should not reach next handler'));
        },
        error: () => {
          // Verify error logging with 'Unknown error'
          expect(errorSpy).toHaveBeenCalledWith(
            expect.stringMatching(/GET \/test - \d+ \(\d+ms\) - Unknown error/),
            expect.stringContaining('"error":"Unknown error"'),
          );

          done();
        },
      });
    });
  });

  describe('client error responses (4xx)', () => {
    it('should log 4xx responses as warnings', (done) => {
      mockResponse.statusCode = 400;
      const responseData = { error: 'Bad Request' };
      (mockCallHandler.handle as jest.Mock).mockReturnValue(of(responseData));

      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      result.subscribe({
        next: () => {
          // Verify warning log for 4xx status
          expect(warnSpy).toHaveBeenCalledWith(
            expect.stringMatching(/GET \/test - 400 \(\d+ms\)/),
            expect.stringContaining('"statusCode":400'),
          );

          done();
        },
        error: done,
      });
    });
  });

  describe('request details logging', () => {
    it('should log user agent and IP address', (done) => {
      const responseData = { message: 'success' };
      (mockCallHandler.handle as jest.Mock).mockReturnValue(of(responseData));

      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      result.subscribe({
        next: () => {
          const logCalls = logSpy.mock.calls as MockLogCall[];
          const requestLogCall = logCalls[0];
          const loggedData = JSON.parse(
            requestLogCall?.[1] ?? '{}',
          ) as LoggedData;

          expect(loggedData.userAgent).toBe('test-user-agent');
          expect(loggedData.ip).toBe('127.0.0.1');

          done();
        },
        error: done,
      });
    });

    it('should handle missing user agent and IP gracefully', (done) => {
      // Mock request without user agent and IP
      (mockRequest.get as jest.Mock).mockReturnValue(undefined);
      (mockRequest as Record<string, unknown>).ip = undefined;
      mockRequest.connection = {} as any;

      const responseData = { message: 'success' };
      (mockCallHandler.handle as jest.Mock).mockReturnValue(of(responseData));

      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      result.subscribe({
        next: () => {
          const logCalls = logSpy.mock.calls as MockLogCall[];
          const requestLogCall = logCalls[0];
          const loggedData = JSON.parse(
            requestLogCall?.[1] ?? '{}',
          ) as LoggedData;

          expect(loggedData.userAgent).toBe('N/A');
          expect(loggedData.ip).toBe('N/A');

          done();
        },
        error: done,
      });
    });
  });

  describe('content length calculation', () => {
    it('should calculate content length for string responses', (done) => {
      const responseData = 'Hello World';
      (mockCallHandler.handle as jest.Mock).mockReturnValue(of(responseData));

      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      result.subscribe({
        next: () => {
          const logCalls = logSpy.mock.calls as MockLogCall[];
          const responseLogCall = logCalls[1];
          const loggedData = JSON.parse(
            responseLogCall?.[1] ?? '{}',
          ) as LoggedData;

          expect(loggedData.contentLength).toBe(
            Buffer.byteLength('Hello World', 'utf8'),
          );

          done();
        },
        error: done,
      });
    });

    it('should calculate content length for object responses', (done) => {
      const responseData = { message: 'Hello World' };
      (mockCallHandler.handle as jest.Mock).mockReturnValue(of(responseData));

      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      result.subscribe({
        next: () => {
          const logCalls = logSpy.mock.calls as MockLogCall[];
          const responseLogCall = logCalls[1];
          const loggedData = JSON.parse(
            responseLogCall?.[1] ?? '{}',
          ) as LoggedData;

          expect(loggedData.contentLength).toBe(
            Buffer.byteLength(JSON.stringify(responseData), 'utf8'),
          );

          done();
        },
        error: done,
      });
    });

    it('should handle undefined response data', (done) => {
      (mockCallHandler.handle as jest.Mock).mockReturnValue(of(undefined));

      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      result.subscribe({
        next: () => {
          const logCalls = logSpy.mock.calls as MockLogCall[];
          const responseLogCall = logCalls[1];
          const loggedData = JSON.parse(
            responseLogCall?.[1] ?? '{}',
          ) as LoggedData;

          expect(loggedData.contentLength).toBeUndefined();

          done();
        },
        error: done,
      });
    });
  });
});
