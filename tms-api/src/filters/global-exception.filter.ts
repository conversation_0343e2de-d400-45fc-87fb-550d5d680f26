/**
 * Global Exception Filter
 *
 * This filter provides standardized error responses for all exceptions thrown
 * throughout the application. It ensures consistent error formatting and
 * includes correlation IDs for request tracing.
 *
 * Features:
 * - Standardized error response format (statusCode, message, details)
 * - Correlation ID inclusion for tracing
 * - Proper HTTP status code mapping
 * - Support for different exception types (validation, authentication, not found)
 * - Detailed error logging for debugging
 */

import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';

/**
 * Standard error response interface
 */
interface ErrorResponse {
  statusCode: number;
  message: string;
  details?: string | object;
  correlationId?: string;
  timestamp: string;
  path: string;
}

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // Extract correlation ID from request
    const correlationId = request.correlationId;

    // Determine status code and error details
    const { statusCode, message, details } =
      this.extractErrorDetails(exception);

    // Create standardized error response
    const errorResponse: ErrorResponse = {
      statusCode,
      message,
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    // Add optional fields if they exist
    if (details) {
      errorResponse.details = details;
    }

    if (correlationId) {
      errorResponse.correlationId = correlationId;
    }

    // Log the error with correlation ID for debugging
    this.logError(exception, correlationId, request);

    // Send the standardized error response
    response.status(statusCode).json(errorResponse);
  }

  /**
   * Extract error details from different exception types
   */
  private extractErrorDetails(exception: unknown): {
    statusCode: number;
    message: string;
    details?: string | object;
  } {
    // Handle NestJS HttpExceptions (BadRequestException, UnauthorizedException, etc.)
    if (exception instanceof HttpException) {
      const status = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      // Handle structured error responses (from our validation pipe, etc.)
      if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
        const responseObj = exceptionResponse as Record<string, unknown>;

        return {
          statusCode: status,
          message: (responseObj.message as string) || exception.message,
          details: (responseObj.details || responseObj.error) as
            | string
            | object,
        };
      }

      // Handle simple string error responses
      return {
        statusCode: status,
        message:
          typeof exceptionResponse === 'string'
            ? exceptionResponse
            : exception.message,
      };
    }

    // Handle generic JavaScript errors
    if (exception instanceof Error) {
      return {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Internal server error',
        details:
          process.env.NODE_ENV === 'development'
            ? exception.message
            : undefined,
      };
    }

    // Handle unknown exception types
    return {
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: 'Internal server error',
      details:
        process.env.NODE_ENV === 'development' ? String(exception) : undefined,
    };
  }

  /**
   * Log error details for debugging and monitoring
   */
  private logError(
    exception: unknown,
    correlationId: string | undefined,
    request: Request,
  ): void {
    const errorContext = {
      correlationId: correlationId || 'N/A',
      method: request.method,
      url: request.url,
      userAgent: request.get('User-Agent') || 'N/A',
      ip: request.ip || request.connection.remoteAddress || 'N/A',
    };

    if (exception instanceof HttpException) {
      const status = exception.getStatus();

      // Log client errors (4xx) as warnings, server errors (5xx) as errors
      if (status >= 500) {
        this.logger.error(
          `HTTP ${status} - ${exception.message}`,
          exception.stack,
          JSON.stringify(errorContext),
        );
      } else {
        this.logger.warn(
          `HTTP ${status} - ${exception.message}`,
          JSON.stringify(errorContext),
        );
      }
    } else if (exception instanceof Error) {
      this.logger.error(
        `Unhandled Error - ${exception.message}`,
        exception.stack,
        JSON.stringify(errorContext),
      );
    } else {
      this.logger.error(
        `Unknown Exception - ${String(exception)}`,
        undefined,
        JSON.stringify(errorContext),
      );
    }
  }
}
