/**
 * Global Exception Filter Unit Tests
 *
 * Tests for the GlobalExceptionFilter functionality.
 * Verifies standardized error response formatting and correlation ID handling.
 */

import {
  ArgumentsHost,
  HttpException,
  HttpStatus,
  BadRequestException,
  UnauthorizedException,
  NotFoundException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { GlobalExceptionFilter } from './global-exception.filter';

// Type definitions for test mocks
interface MockRequest {
  url?: string;
  method?: string;
  ip?: string;
  get?: jest.Mock;
  correlationId?: string;
  connection?: { remoteAddress: string };
}

interface MockResponse extends Partial<Response> {
  status: jest.Mock;
  json: jest.Mock;
}

interface MockArgumentsHost extends ArgumentsHost {
  switchToHttp: jest.Mock;
}

// Type guard for mock response body
function isMockResponseBody(body: unknown): body is Record<string, unknown> {
  return typeof body === 'object' && body !== null;
}

// Helper function to get mock response call arguments
function getMockResponseBody(
  mockResponse: MockResponse,
): Record<string, unknown> {
  const mockCalls = mockResponse.json.mock.calls as unknown[][];
  if (mockCalls.length === 0) {
    throw new Error('No mock calls found');
  }

  const firstCall = mockCalls[0];
  if (!firstCall || firstCall.length === 0) {
    throw new Error('No arguments in first mock call');
  }

  const body = firstCall[0];
  if (!isMockResponseBody(body)) {
    throw new Error('Mock response body does not have expected structure');
  }

  return body;
}

// Helper function to safely expect mock calls
function expectMockResponseJson(
  mockResponse: MockResponse,
  expectedResponse: Record<string, unknown>,
): void {
  const mockFn = mockResponse.json as jest.MockedFunction<
    typeof mockResponse.json
  >;
  expect(mockFn).toHaveBeenCalledWith(expectedResponse);
}

function expectMockResponseStatus(
  mockResponse: MockResponse,
  expectedStatus: number,
): void {
  const mockFn = mockResponse.status as jest.MockedFunction<
    typeof mockResponse.status
  >;
  expect(mockFn).toHaveBeenCalledWith(expectedStatus);
}

describe('GlobalExceptionFilter', () => {
  let filter: GlobalExceptionFilter;
  let mockArgumentsHost: MockArgumentsHost;
  let mockRequest: MockRequest;
  let mockResponse: MockResponse;

  beforeEach(() => {
    filter = new GlobalExceptionFilter();

    mockRequest = {
      url: '/test-endpoint',
      method: 'GET',
      ip: '127.0.0.1',
      get: jest.fn().mockReturnValue('test-user-agent'),
      connection: { remoteAddress: '127.0.0.1' },
      correlationId: '123e4567-e89b-12d3-a456-426614174000',
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnValue({
        getRequest: () => mockRequest,
        getResponse: () => mockResponse,
      }),
    } as MockArgumentsHost;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(filter).toBeDefined();
  });

  describe('catch', () => {
    it('should handle BadRequestException with structured response', () => {
      // Arrange
      const exception = new BadRequestException({
        statusCode: 400,
        message: 'Validation failed',
        details: { field: 'username', issue: 'required' },
      });

      // Act
      filter.catch(exception, mockArgumentsHost);

      // Assert
      expectMockResponseStatus(mockResponse, 400);
      expectMockResponseJson(mockResponse, {
        statusCode: 400,
        message: 'Validation failed',
        details: { field: 'username', issue: 'required' },
        correlationId: '123e4567-e89b-12d3-a456-426614174000',
        timestamp: expect.any(String),
        path: '/test-endpoint',
      });
    });

    it('should handle UnauthorizedException with simple message', () => {
      // Arrange
      const exception = new UnauthorizedException('Invalid credentials');

      // Act
      filter.catch(exception, mockArgumentsHost);

      // Assert
      expectMockResponseStatus(mockResponse, 401);
      expectMockResponseJson(mockResponse, {
        statusCode: 401,
        message: 'Invalid credentials',
        details: 'Unauthorized',
        correlationId: '123e4567-e89b-12d3-a456-426614174000',
        timestamp: expect.any(String),
        path: '/test-endpoint',
      });
    });

    it('should handle NotFoundException', () => {
      // Arrange
      const exception = new NotFoundException('Resource not found');

      // Act
      filter.catch(exception, mockArgumentsHost);

      // Assert
      expectMockResponseStatus(mockResponse, 404);
      expectMockResponseJson(mockResponse, {
        statusCode: 404,
        message: 'Resource not found',
        details: 'Not Found',
        correlationId: '123e4567-e89b-12d3-a456-426614174000',
        timestamp: expect.any(String),
        path: '/test-endpoint',
      });
    });

    it('should handle generic Error as internal server error', () => {
      // Arrange
      const exception = new Error('Something went wrong');

      // Act
      filter.catch(exception, mockArgumentsHost);

      // Assert
      expectMockResponseStatus(mockResponse, 500);
      expectMockResponseJson(mockResponse, {
        statusCode: 500,
        message: 'Internal server error',
        correlationId: '123e4567-e89b-12d3-a456-426614174000',
        timestamp: expect.any(String),
        path: '/test-endpoint',
      });
    });

    it('should handle unknown exception types', () => {
      // Arrange
      const exception = 'Unknown error string';

      // Act
      filter.catch(exception, mockArgumentsHost);

      // Assert
      expectMockResponseStatus(mockResponse, 500);
      expectMockResponseJson(mockResponse, {
        statusCode: 500,
        message: 'Internal server error',
        correlationId: '123e4567-e89b-12d3-a456-426614174000',
        timestamp: expect.any(String),
        path: '/test-endpoint',
      });
    });

    it('should work without correlation ID', () => {
      // Arrange
      mockRequest.correlationId = undefined;
      const exception = new BadRequestException('Test error');

      // Act
      filter.catch(exception, mockArgumentsHost);

      // Assert
      expectMockResponseStatus(mockResponse, 400);
      expectMockResponseJson(mockResponse, {
        statusCode: 400,
        message: 'Test error',
        details: 'Bad Request',
        timestamp: expect.any(String),
        path: '/test-endpoint',
      });
    });

    it('should include error details from structured HttpException response', () => {
      // Arrange
      const exception = new HttpException(
        {
          statusCode: 422,
          message: 'Unprocessable Entity',
          error: 'Validation Error',
          details: ['Field A is required', 'Field B must be a number'],
        },
        HttpStatus.UNPROCESSABLE_ENTITY,
      );

      // Act
      filter.catch(exception, mockArgumentsHost);

      // Assert
      expectMockResponseStatus(mockResponse, 422);
      expectMockResponseJson(mockResponse, {
        statusCode: 422,
        message: 'Unprocessable Entity',
        details: ['Field A is required', 'Field B must be a number'],
        correlationId: '123e4567-e89b-12d3-a456-426614174000',
        timestamp: expect.any(String),
        path: '/test-endpoint',
      });
    });

    it('should handle HttpException with string response', () => {
      // Arrange
      const exception = new HttpException(
        'Custom error message',
        HttpStatus.CONFLICT,
      );

      // Act
      filter.catch(exception, mockArgumentsHost);

      // Assert
      expectMockResponseStatus(mockResponse, 409);
      expectMockResponseJson(mockResponse, {
        statusCode: 409,
        message: 'Custom error message',
        correlationId: '123e4567-e89b-12d3-a456-426614174000',
        timestamp: expect.any(String),
        path: '/test-endpoint',
      });
    });

    it('should format timestamp as ISO string', () => {
      // Arrange
      const exception = new BadRequestException('Test error');
      const beforeTime = new Date();

      // Act
      filter.catch(exception, mockArgumentsHost);

      // Assert
      const callArgs = getMockResponseBody(mockResponse);
      const timestampValue = callArgs.timestamp;
      if (typeof timestampValue !== 'string') {
        throw new Error('Expected timestamp to be a string');
      }
      const timestamp = new Date(timestampValue);
      const afterTime = new Date();

      expect(timestamp).toBeInstanceOf(Date);
      expect(timestamp.getTime()).toBeGreaterThanOrEqual(beforeTime.getTime());
      expect(timestamp.getTime()).toBeLessThanOrEqual(afterTime.getTime());
    });
  });
});
