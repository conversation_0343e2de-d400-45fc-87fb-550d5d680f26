/**
 * Global Exception Filter Integration Tests
 *
 * These tests verify that the GlobalExceptionFilter works correctly
 * with the entire application stack including middleware, guards, and pipes.
 */

import { Test } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import request from 'supertest';
import { Server } from 'http';
import { AppModule } from '../app.module';
import { GlobalExceptionFilter } from './global-exception.filter';
import { ValidationPipe } from '../pipes/validation.pipe';
import { cleanupDatabaseWithCallback } from '../../test/utils/database-cleanup';
import { TEST_CREDENTIALS } from '../../test/test-constants';

// Type definitions for test responses
interface ErrorResponse {
  statusCode: number;
  message: string;
  timestamp: string;
  path: string;
  correlationId?: string;
  details?: unknown;
}

interface SuccessResponse {
  message: string;
  correlationId: string;
  timestamp: string;
}

// Helper function to get supertest instance
function getHttpServer(app: INestApplication) {
  return request(app.getHttpServer() as Server);
}

// Type guard functions for response bodies
function isErrorResponse(body: unknown): body is ErrorResponse {
  return (
    typeof body === 'object' &&
    body !== null &&
    'statusCode' in body &&
    'message' in body &&
    'timestamp' in body &&
    'path' in body
  );
}

function isSuccessResponse(body: unknown): body is SuccessResponse {
  return (
    typeof body === 'object' &&
    body !== null &&
    'message' in body &&
    'timestamp' in body
  );
}

// Helper function to safely expect error response properties
function expectErrorResponse(
  response: request.Response,
  expectedProps: Record<string, unknown>,
): void {
  if (!isErrorResponse(response.body)) {
    throw new Error('Response body is not a valid ErrorResponse');
  }
  expect(response.body).toMatchObject(expectedProps);
}

function expectSuccessResponse(
  response: request.Response,
  expectedProps: Record<string, unknown>,
): void {
  if (!isSuccessResponse(response.body)) {
    throw new Error('Response body is not a valid SuccessResponse');
  }
  expect(response.body).toMatchObject(expectedProps);
}

function expectErrorResponseNotToHaveProperty(
  response: request.Response,
  property: string,
): void {
  if (!isErrorResponse(response.body)) {
    throw new Error('Response body is not a valid ErrorResponse');
  }
  expect(response.body).not.toHaveProperty(property);
}

function expectErrorResponseFieldTypes(response: request.Response): void {
  if (!isErrorResponse(response.body)) {
    throw new Error('Response body is not a valid ErrorResponse');
  }

  const errorResponse = response.body;

  // Verify all required fields are present
  expect(errorResponse).toHaveProperty('statusCode');
  expect(errorResponse).toHaveProperty('message');
  expect(errorResponse).toHaveProperty('timestamp');
  expect(errorResponse).toHaveProperty('path');
  expect(errorResponse).toHaveProperty('correlationId');

  // Verify field types
  expect(typeof errorResponse.statusCode).toBe('number');
  expect(typeof errorResponse.message).toBe('string');
  expect(typeof errorResponse.timestamp).toBe('string');
  expect(typeof errorResponse.path).toBe('string');
  expect(typeof errorResponse.correlationId).toBe('string');

  // Verify timestamp is valid ISO string
  expect(() => new Date(errorResponse.timestamp)).not.toThrow();
}

describe('GlobalExceptionFilter (Integration)', () => {
  let app: INestApplication;
  let dataSource: DataSource;

  beforeAll(async () => {
    // Environment variables are now loaded from .env file

    // Create a test module manually to configure global filters before initialization
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();

    // Configure global pipes and filters BEFORE initialization
    app.useGlobalPipes(new ValidationPipe());
    app.useGlobalFilters(new GlobalExceptionFilter());

    // Now initialize the app with the global configurations
    await app.init();

    // Get the database connection
    dataSource = moduleFixture.get<DataSource>(DataSource);
  }, 60000);

  afterAll((done) => {
    cleanupDatabaseWithCallback(dataSource, app, done);
  }, 60000);

  describe('Correlation ID middleware errors', () => {
    it('should return standardized error response when correlation ID is missing', async () => {
      const response = await getHttpServer(app)
        .get('/correlation-test')
        .expect(400);

      expectErrorResponse(response, {
        statusCode: 400,
        message: 'X-Correlation-ID header is required',
        timestamp: expect.any(String),
        path: '/',
      });

      // Should not have correlation ID since it was missing
      expectErrorResponseNotToHaveProperty(response, 'correlationId');
    });

    it('should return standardized error response when correlation ID is invalid', async () => {
      const response = await getHttpServer(app)
        .get('/correlation-test')
        .set('X-Correlation-ID', 'invalid-uuid')
        .expect(400);

      expectErrorResponse(response, {
        statusCode: 400,
        message: 'X-Correlation-ID must be a valid UUID',
        timestamp: expect.any(String),
        path: '/',
      });

      // Should not have correlation ID since it was invalid
      expectErrorResponseNotToHaveProperty(response, 'correlationId');
    });
  });

  describe('Authentication errors', () => {
    it('should return standardized error response for missing authentication', async () => {
      const correlationId = uuidv4();

      const response = await getHttpServer(app)
        .get('/protected')
        .set('X-Correlation-ID', correlationId)
        .expect(401);

      expectErrorResponse(response, {
        statusCode: 401,
        message: 'Unauthorized',
        correlationId,
        timestamp: expect.any(String),
        path: '/protected',
      });
    });

    it('should return standardized error response for invalid credentials', async () => {
      const correlationId = uuidv4();

      const response = await getHttpServer(app)
        .get('/protected')
        .set('X-Correlation-ID', correlationId)
        .auth('wrong-user', 'wrong-pass')
        .expect(401);

      expectErrorResponse(response, {
        statusCode: 401,
        message: 'Invalid credentials',
        correlationId,
        timestamp: expect.any(String),
        path: '/protected',
      });
    });
  });

  describe('Not found errors', () => {
    it('should return standardized error response for non-existent endpoints', async () => {
      const correlationId = uuidv4();

      const response = await getHttpServer(app)
        .get('/non-existent-endpoint')
        .set('X-Correlation-ID', correlationId)
        .expect(404);

      expectErrorResponse(response, {
        statusCode: 404,
        message: expect.stringContaining('Cannot GET /non-existent-endpoint'),
        correlationId,
        timestamp: expect.any(String),
        path: '/non-existent-endpoint',
      });
    });
  });

  describe('Successful requests', () => {
    it('should not interfere with successful requests', async () => {
      const correlationId = uuidv4();

      const response = await getHttpServer(app)
        .get('/correlation-test')
        .set('X-Correlation-ID', correlationId)
        .expect(200);

      expectSuccessResponse(response, {
        message: 'Correlation ID test endpoint',
        correlationId,
        timestamp: expect.any(String),
      });
    });

    it('should not interfere with authenticated requests', async () => {
      const correlationId = uuidv4();

      const response = await getHttpServer(app)
        .get('/protected')
        .set('X-Correlation-ID', correlationId)
        .auth(TEST_CREDENTIALS.USERNAME, TEST_CREDENTIALS.PASSWORD)
        .expect(200);

      expectSuccessResponse(response, {
        message: 'This is a protected endpoint that requires Basic Auth!',
        correlationId,
        timestamp: expect.any(String),
      });
    });
  });

  describe('Error response format validation', () => {
    it('should always include required fields in error responses', async () => {
      const correlationId = uuidv4();

      const response = await getHttpServer(app)
        .get('/non-existent')
        .set('X-Correlation-ID', correlationId)
        .expect(404);

      expectErrorResponseFieldTypes(response);
    });

    it('should include correlation ID in response headers when available', async () => {
      const correlationId = uuidv4();

      const response = await getHttpServer(app)
        .get('/correlation-test')
        .set('X-Correlation-ID', correlationId)
        .expect(200);

      expect(response.headers).toHaveProperty(
        'x-correlation-id',
        correlationId,
      );
    });
  });
});
