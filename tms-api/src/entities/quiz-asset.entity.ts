/**
 * Quiz Asset Entity
 *
 * This class extends BaseEntity and represents an asset (like a GIF) associated with a quiz.
 * It provides fields for storing asset metadata and references to the actual files in Minio.
 */

import { Entity, Column, ManyToMany, JoinTable } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Quiz } from './quiz.entity';

/**
 * Enum for asset types
 */
export enum AssetType {
  QUESTION = 'question',
  SOLUTION = 'solution',
}

/**
 * Quiz Asset entity representing assets associated with quizzes
 */
@Entity('quiz_assets')
export class QuizAsset extends BaseEntity {
  @Column({ name: 'asset_id', length: 100 })
  assetId!: string;

  @Column({
    type: 'enum',
    enum: AssetType,
    default: AssetType.QUESTION,
  })
  type: AssetType = AssetType.QUESTION;

  // url property removed as requested

  @Column({ name: 'file_path', length: 500 })
  filePath!: string;

  @Column({ name: 'file_size' })
  fileSize!: number;

  @Column({ name: 'mime_type', length: 100 })
  mimeType!: string;

  @Column({ name: 'original_filename', length: 255, nullable: true })
  originalFilename!: string;

  @Column({ nullable: true })
  width!: number;

  @Column({ nullable: true })
  height!: number;

  @ManyToMany(() => Quiz, (quiz) => quiz.assets)
  @JoinTable({
    name: 'quiz_asset_relations',
    joinColumn: {
      name: 'asset_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'quiz_id',
      referencedColumnName: 'id',
    },
  })
  quizzes!: Quiz[];

  /**
   * Constructor with default values
   */
  constructor(partial?: Partial<QuizAsset>) {
    super();

    // Apply any provided properties
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
