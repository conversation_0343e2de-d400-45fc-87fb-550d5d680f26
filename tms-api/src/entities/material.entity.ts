/**
 * Material Entity
 *
 * This abstract class extends BaseEntity and serves as the foundation for all teaching materials.
 * It provides common fields and functionality that all material types (Quiz, Homework, LectureNotes) will inherit.
 */

import { Column, Entity, Index, Check, BeforeInsert } from 'typeorm';
import { BaseEntity } from './base.entity';

/**
 * Enum for material categories as defined in the PRD
 */
export enum MaterialCategory {
  QUIZ = 'quizzes',
  HOMEWORK = 'homework',
  LECTURE_NOTES = 'lecturenotes',
}

/**
 * Enum for material versions as defined in the PRD
 */
export enum MaterialVersion {
  STUDENT_COPY = 'student-copy',
  WORKED_SOLUTIONS = 'worked-solutions',
}

/**
 * Enum for week types as defined in the PRD
 */
export enum WeekType {
  NORMAL = 'normal',
  HOLIDAY = 'holiday',
}

/**
 * Abstract material entity that all specific material types will extend
 * Provides common fields for all teaching materials
 */
@Entity()
@Index([
  'subject',
  'grade',
  'year',
  'term',
  'week',
  'weekType',
  'classLevel',
  'course',
])
export abstract class Material extends BaseEntity {
  @Column({ length: 100 })
  subject!: string;

  @Column()
  @Check('grade > 0 AND grade <= 12')
  grade!: number;

  @Column({ length: 50 })
  classLevel!: string;

  @Column({ length: 255 })
  lessonName!: string;

  @Column()
  @Check('year >= 2000 AND year <= 2100')
  year!: number;

  @Column()
  @Check('term > 0 AND term <= 4')
  term!: number;

  @Column()
  @Check('week > 0 AND week <= 52')
  week!: number;

  @Column({
    type: 'enum',
    enum: WeekType,
    default: WeekType.NORMAL,
  })
  weekType!: WeekType;

  @Column({ length: 100, nullable: true })
  course!: string;

  @Column({ length: 255, nullable: true })
  teachingProgram!: string;

  @Column({
    type: 'enum',
    enum: MaterialCategory,
  })
  category!: MaterialCategory;

  @Column({
    type: 'enum',
    enum: MaterialVersion,
  })
  versionType!: MaterialVersion;

  @Column({ name: 'original_filename', length: 255 })
  originalFilename!: string;

  @Column({ name: 'upload_timestamp', type: 'timestamp' })
  uploadTimestamp!: Date;

  @Column({ type: 'jsonb', nullable: true })
  internalMetadata!: Record<string, unknown>;

  // File-related properties removed as requested

  /**
   * Constructor with default values
   */
  constructor(partial?: Partial<Material>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }

  /**
   * Lifecycle hook that runs before entity insertion
   * Sets default upload timestamp if not provided
   */
  @BeforeInsert()
  protected beforeInsert(): void {
    super.beforeInsert();
    if (!this.uploadTimestamp) {
      this.uploadTimestamp = new Date();
    }
  }
}
