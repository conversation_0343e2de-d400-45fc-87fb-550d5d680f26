/**
 * Base Entity
 *
 * This abstract class serves as the foundation for all entities in the system.
 * It provides common fields and functionality that all entities will inherit.
 */

import {
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  Column,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';

/**
 * Base entity class that all other entities will extend
 * Provides common fields like id, created/updated timestamps, and soft delete support
 */
export abstract class BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt!: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt!: Date;

  @DeleteDateColumn({ name: 'deleted_at', nullable: true })
  deletedAt?: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy?: string;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy?: string;

  /**
   * Lifecycle hook that runs before entity insertion
   * Can be extended by child classes to add custom logic
   */
  @BeforeInsert()
  protected beforeInsert(): void {
    // This method can be overridden by child classes
    // to add custom logic before insertion
  }

  /**
   * Lifecycle hook that runs before entity update
   * Can be extended by child classes to add custom logic
   */
  @BeforeUpdate()
  protected beforeUpdate(): void {
    // This method can be overridden by child classes
    // to add custom logic before update
  }
}
