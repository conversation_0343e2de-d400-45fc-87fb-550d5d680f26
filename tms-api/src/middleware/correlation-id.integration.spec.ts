/**
 * Correlation ID Middleware Integration Tests
 *
 * Integration tests for correlation ID middleware with real HTTP requests.
 */

import { TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AppModule } from '../app.module';
import { cleanupDatabase } from '../../test/utils/database-cleanup';
import { createTestModule } from '../../test/utils/test-module-factory';
import { TEST_CREDENTIALS } from '../../test/test-constants';
import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Server } from 'http';

// Helper function to get typed HTTP server for supertest
// Since we're using Express (NestJS default), we can safely cast to Node.js Server
function getHttpServer(app: INestApplication) {
  return request(app.getHttpServer() as Server);
}

describe('Correlation ID Middleware Integration', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let module: TestingModule;

  beforeAll(async () => {
    // Create test module with real database connection
    module = await createTestModule({
      imports: [AppModule],
      isGlobalConfig: true,
    });

    app = module.createNestApplication();
    await app.init();

    dataSource = module.get(DataSource);
  });

  afterAll(async () => {
    await cleanupDatabase(dataSource, module);
    await app.close();
  }, 60000);

  describe('Correlation ID validation', () => {
    it('should return 400 when X-Correlation-ID header is missing', async () => {
      const response = await getHttpServer(app)
        .get('/correlation-test')
        .expect(400);

      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty(
        'message',
        'X-Correlation-ID header is required',
      );
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });

    it('should return 400 when X-Correlation-ID is not a valid UUID', async () => {
      const response = await getHttpServer(app)
        .get('/correlation-test')
        .set('X-Correlation-ID', 'invalid-uuid')
        .expect(400);

      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty(
        'message',
        'X-Correlation-ID must be a valid UUID',
      );
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });

    it('should return 400 when X-Correlation-ID is empty string', async () => {
      const response = await getHttpServer(app)
        .get('/correlation-test')
        .set('X-Correlation-ID', '')
        .expect(400);

      expect(response.body).toHaveProperty('statusCode', 400);
      expect(response.body).toHaveProperty(
        'message',
        'X-Correlation-ID header is required',
      );
    });

    it('should return 200 and include correlation ID when valid UUID is provided', async () => {
      const correlationId = uuidv4();

      const response = await getHttpServer(app)
        .get('/correlation-test')
        .set('X-Correlation-ID', correlationId)
        .expect(200);

      expect(response.body).toHaveProperty(
        'message',
        'Correlation ID test endpoint',
      );
      expect(response.body).toHaveProperty('correlationId', correlationId);
      expect(response.body).toHaveProperty('timestamp');
      expect(response.headers).toHaveProperty(
        'x-correlation-id',
        correlationId,
      );
    });

    it('should work with uppercase UUID', async () => {
      const correlationId = uuidv4().toUpperCase();

      const response = await getHttpServer(app)
        .get('/correlation-test')
        .set('X-Correlation-ID', correlationId)
        .expect(200);

      expect(response.body).toHaveProperty('correlationId', correlationId);
      expect(response.headers).toHaveProperty(
        'x-correlation-id',
        correlationId,
      );
    });
  });

  describe('Protected endpoint with correlation ID', () => {
    it('should require both authentication and correlation ID', async () => {
      const correlationId = uuidv4();

      const response = await getHttpServer(app)
        .get('/protected')
        .set('X-Correlation-ID', correlationId)
        .auth(TEST_CREDENTIALS.USERNAME, TEST_CREDENTIALS.PASSWORD)
        .expect(200);

      expect(response.body).toHaveProperty(
        'message',
        'This is a protected endpoint that requires Basic Auth!',
      );
      expect(response.body).toHaveProperty('correlationId', correlationId);
      expect(response.body).toHaveProperty('timestamp');
      expect(response.headers).toHaveProperty(
        'x-correlation-id',
        correlationId,
      );
    });

    it('should return 400 for missing correlation ID even with valid auth', async () => {
      const response = await getHttpServer(app)
        .get('/protected')
        .auth(TEST_CREDENTIALS.USERNAME, TEST_CREDENTIALS.PASSWORD)
        .expect(400);

      expect(response.body).toHaveProperty(
        'message',
        'X-Correlation-ID header is required',
      );
    });
  });

  describe('Health endpoint with correlation ID', () => {
    it('should require correlation ID for health endpoint', async () => {
      const response = await getHttpServer(app).get('/health').expect(400);

      expect(response.body).toHaveProperty(
        'message',
        'X-Correlation-ID header is required',
      );
    });

    it('should return health status with valid correlation ID', async () => {
      const correlationId = uuidv4();

      const response = await getHttpServer(app)
        .get('/health')
        .set('X-Correlation-ID', correlationId)
        .expect(200);

      expect(response.body).toHaveProperty('status'); // Can be 'ok' or 'degraded' depending on Minio
      expect(response.headers).toHaveProperty(
        'x-correlation-id',
        correlationId,
      );
    });
  });
});
