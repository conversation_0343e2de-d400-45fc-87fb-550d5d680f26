/**
 * Correlation ID Middleware Unit Tests
 *
 * Tests for the CorrelationIdMiddleware functionality.
 */

import { BadRequestException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { CorrelationIdMiddleware } from './correlation-id.middleware';

describe('CorrelationIdMiddleware', () => {
  let middleware: CorrelationIdMiddleware;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    middleware = new CorrelationIdMiddleware();
    mockRequest = {
      headers: {},
    };
    mockResponse = {
      setHeader: jest.fn(),
    };
    mockNext = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(middleware).toBeDefined();
  });

  describe('use', () => {
    it('should pass through with valid UUID correlation ID', () => {
      // Arrange
      const validUuid = '123e4567-e89b-12d3-a456-************';
      mockRequest.headers = {
        'x-correlation-id': validUuid,
      };

      // Act
      middleware.use(
        mockRequest as Request,
        mockResponse as Response,
        mockNext,
      );

      // Assert
      expect(mockRequest.correlationId).toBe(validUuid);
      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'x-correlation-id',
        validUuid,
      );
      expect(mockNext).toHaveBeenCalled();
    });

    it('should throw BadRequestException when correlation ID is missing', () => {
      // Arrange
      mockRequest.headers = {}; // No correlation ID header

      // Act & Assert
      expect(() => {
        middleware.use(
          mockRequest as Request,
          mockResponse as Response,
          mockNext,
        );
      }).toThrow(BadRequestException);

      expect(() => {
        middleware.use(
          mockRequest as Request,
          mockResponse as Response,
          mockNext,
        );
      }).toThrow('X-Correlation-ID header is required');

      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException when correlation ID is empty string', () => {
      // Arrange
      mockRequest.headers = {
        'x-correlation-id': '',
      };

      // Act & Assert
      expect(() => {
        middleware.use(
          mockRequest as Request,
          mockResponse as Response,
          mockNext,
        );
      }).toThrow(BadRequestException);

      expect(() => {
        middleware.use(
          mockRequest as Request,
          mockResponse as Response,
          mockNext,
        );
      }).toThrow('X-Correlation-ID header is required');

      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException when correlation ID is not a valid UUID', () => {
      // Arrange
      mockRequest.headers = {
        'x-correlation-id': 'invalid-uuid',
      };

      // Act & Assert
      expect(() => {
        middleware.use(
          mockRequest as Request,
          mockResponse as Response,
          mockNext,
        );
      }).toThrow(BadRequestException);

      expect(() => {
        middleware.use(
          mockRequest as Request,
          mockResponse as Response,
          mockNext,
        );
      }).toThrow('X-Correlation-ID must be a valid UUID');

      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException for malformed UUID', () => {
      // Arrange
      mockRequest.headers = {
        'x-correlation-id': '123e4567-e89b-12d3-a456-42661417400', // Missing last digit
      };

      // Act & Assert
      expect(() => {
        middleware.use(
          mockRequest as Request,
          mockResponse as Response,
          mockNext,
        );
      }).toThrow(BadRequestException);

      expect(() => {
        middleware.use(
          mockRequest as Request,
          mockResponse as Response,
          mockNext,
        );
      }).toThrow('X-Correlation-ID must be a valid UUID');

      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle UUID v4 format correctly', () => {
      // Arrange
      const uuidV4 = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
      mockRequest.headers = {
        'x-correlation-id': uuidV4,
      };

      // Act
      middleware.use(
        mockRequest as Request,
        mockResponse as Response,
        mockNext,
      );

      // Assert
      expect(mockRequest.correlationId).toBe(uuidV4);
      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'x-correlation-id',
        uuidV4,
      );
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle uppercase UUID correctly', () => {
      // Arrange
      const uppercaseUuid = 'F47AC10B-58CC-4372-A567-0E02B2C3D479';
      mockRequest.headers = {
        'x-correlation-id': uppercaseUuid,
      };

      // Act
      middleware.use(
        mockRequest as Request,
        mockResponse as Response,
        mockNext,
      );

      // Assert
      expect(mockRequest.correlationId).toBe(uppercaseUuid);
      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'x-correlation-id',
        uppercaseUuid,
      );
      expect(mockNext).toHaveBeenCalled();
    });
  });
});
