{"name": "tms-api", "version": "0.0.1", "description": "Teaching Material System (TMS) REST API - A centralized service for storing, retrieving, updating, and deleting F2F paperless marking worked solution quiz materials", "author": "<PERSON>", "private": true, "license": "UNLICENSED", "keywords": ["teaching", "education", "api", "<PERSON><PERSON><PERSON>", "typescript", "postgresql", "docker", "quiz", "f2f", "paperless-marking", "worked-solutions"], "repository": {"type": "git", "url": "git+https://github.com/williamdu/tms.git"}, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --force<PERSON>xit", "test:watch": "jest --watch", "test:cov": "npx ts-node scripts/ensure-db-running.ts && jest --coverage --forceExit && jest --config ./test/jest-e2e.json --forceExit", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json --forceExit", "test:performance": "jest --config ./test/jest-e2e.json --testPathPattern=performance --forceExit --runInBand", "test:all": "npx ts-node scripts/ensure-db-running.ts && npm run test && npm run test:e2e", "db:start": "docker-compose up -d postgres && sleep 5 && echo 'PostgreSQL development database is ready'", "docker:build:dev": "docker build -t tms-api-dev-container:latest --target development .", "docker:build:prod": "docker build -t tms-api-prod-container:latest --target production .", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "typeorm": "typeorm-ts-node-commonjs", "migration:create": "npm run typeorm -- migration:create", "migration:generate": "npm run typeorm -- -d ./src/config/typeorm.config.ts migration:generate", "migration:run": "npx ts-node --transpile-only -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:run -d ./src/config/typeorm.config.ts", "migration:revert": "npx ts-node --transpile-only -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:revert -d ./src/config/typeorm.config.ts", "migration:show": "npm run typeorm -- -d ./src/config/typeorm.config.ts migration:show", "api:generate": "npx ts-node scripts/generate-api-spec.ts", "api:validate": "npx ts-node scripts/validate-api-spec.ts", "api:deploy-docs": "npx ts-node scripts/generate-deployment-docs.ts", "api:docs": "npm run api:generate && npm run api:validate && npm run api:deploy-docs", "test:api-docs": "jest --config ./test/jest-e2e.json --testPathPattern=api-documentation-accuracy --forceExit", "test:api-spec": "jest --config ./test/jest-e2e.json --testPathPattern=api-specification-validation --forceExit"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.2.0", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@types/minio": "^7.1.0", "@types/multer": "^1.4.12", "@types/yauzl": "^2.10.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compression": "^1.8.0", "helmet": "^8.1.0", "minio": "^8.0.5", "passport": "^0.7.0", "passport-http": "^0.3.0", "pg": "^8.16.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "typeorm": "^0.3.24", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "yauzl": "^3.2.0", "zod": "^3.25.28"}, "devDependencies": {"@apidevtools/swagger-parser": "^10.1.1", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/compression": "^1.8.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/passport": "^1.0.17", "@types/passport-http": "^0.3.11", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "js-yaml": "^4.1.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "overrides": {"glob": "^10.0.0"}}