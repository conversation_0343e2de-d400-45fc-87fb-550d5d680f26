# Usage Examples

This document provides practical examples for using the TMS REST API.

## Prerequisites

- Valid authentication credentials
- API base URL
- HTTP client (curl, Postman, or programming language HTTP library)

## Common Headers

All examples use these headers:
```
Authorization: Basic dG1zLXVzZXJuYW1lOnRtcy1wYXNzd29yZA==
X-Correlation-ID: 123e4567-e89b-12d3-a456-************
Content-Type: application/json (for JSON requests)
```

## Health Check

### Check API Status
```bash
curl -X GET "https://api.example.com/health"
```

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2025-01-26T10:30:00.000Z",
  "uptime": 3600,
  "environment": "production"
}
```

## Quiz Management

### 1. Retrieve Quiz Materials

```bash
curl -X GET "https://api.example.com/quiz/f2f/paperless-marking-worked-solutions" \
  -H "Authorization: Basic dG1zLXVzZXJuYW1lOnRtcy1wYXNzd29yZA==" \
  -H "X-Correlation-ID: 123e4567-e89b-12d3-a456-************" \
  -G \
  -d "grade=12" \
  -d "subject=Math" \
  -d "course=3U" \
  -d "classLevel=A1" \
  -d "color=R" \
  -d "year=2025" \
  -d "term=2" \
  -d "week=4" \
  -d "weekType=normal"
```

**Response:**
```json
{
  "message": "Quiz materials retrieved successfully",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "grade": 12,
      "subject": "Math",
      "course": "3U",
      "classLevel": "A1",
      "color": "R",
      "year": 2025,
      "term": 2,
      "week": 4,
      "weekType": "normal",
      "gifUrls": [
        {
          "id": "1137",
          "url": "https://storage.example.com/quiz-assets/1137.gif?signature=..."
        }
      ],
      "uploadTimestamp": "2025-01-26T10:00:00.000Z"
    }
  ]
}
```

### 2. Upload Quiz Materials

```bash
curl -X POST "https://api.example.com/quiz/f2f/paperless-marking-worked-solutions" \
  -H "Authorization: Basic dG1zLXVzZXJuYW1lOnRtcy1wYXNzd29yZA==" \
  -H "X-Correlation-ID: 123e4567-e89b-12d3-a456-************" \
  -F "file=@quiz-materials.zip" \
  -F "year=2025" \
  -F "term=2" \
  -F "week=4" \
  -F "weekType=normal" \
  -F "teachingProgram=St George Girls"
```

**Response:**
```json
{
  "message": "Quiz uploaded and processed successfully",
  "data": {
    "quizId": 123,
    "metadataPath": "quiz-metadata/2025/term-2/week-4/metadata.json",
    "uploadedGifs": [
      {
        "id": "1137",
        "url": "https://storage.example.com/quiz-assets/1137.gif?signature=..."
      },
      {
        "id": "2013",
        "url": "https://storage.example.com/quiz-assets/2013.gif?signature=..."
      }
    ],
    "gifCount": 2,
    "extractedMetadata": {
      "grade": 12,
      "subject": "Math",
      "course": "3U",
      "classLevel": "A1",
      "color": "R",
      "topic": "Trigonometry"
    },
    "uploadTimestamp": "2025-01-26T10:30:00.000Z"
  }
}
```

### 3. Update Quiz Materials

```bash
curl -X PUT "https://api.example.com/quiz/550e8400-e29b-41d4-a716-************" \
  -H "Authorization: Basic dG1zLXVzZXJuYW1lOnRtcy1wYXNzd29yZA==" \
  -H "X-Correlation-ID: 123e4567-e89b-12d3-a456-************" \
  -F "file=@updated-quiz-materials.zip" \
  -F "teachingProgram=Updated Program"
```

### 4. Delete Quiz Materials

```bash
curl -X DELETE "https://api.example.com/quiz/550e8400-e29b-41d4-a716-************" \
  -H "Authorization: Basic dG1zLXVzZXJuYW1lOnRtcy1wYXNzd29yZA==" \
  -H "X-Correlation-ID: 123e4567-e89b-12d3-a456-************"
```

**Response:**
```json
{
  "message": "Quiz deleted successfully",
  "data": {
    "deletedQuizId": "550e8400-e29b-41d4-a716-************",
    "deletedAssets": 2,
    "deletionTimestamp": "2025-01-26T11:00:00.000Z"
  }
}
```

## Error Handling Examples

### Validation Error (400)
```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "details": [
    {
      "field": "grade",
      "value": "invalid",
      "constraints": {
        "isNumber": "grade must be a number",
        "min": "grade must not be less than 1",
        "max": "grade must not be greater than 12"
      }
    }
  ],
  "correlationId": "123e4567-e89b-12d3-a456-************"
}
```

### Not Found Error (404)
```json
{
  "statusCode": 404,
  "message": "Quiz not found",
  "details": "No quiz found with ID: 550e8400-e29b-41d4-a716-************",
  "correlationId": "123e4567-e89b-12d3-a456-************"
}
```

### File Too Large Error (413)
```json
{
  "statusCode": 413,
  "message": "File too large",
  "details": "File size exceeds maximum limit of 50MB",
  "correlationId": "123e4567-e89b-12d3-a456-************"
}
```

## Programming Language Examples

### JavaScript (Node.js with Axios)
```javascript
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

const client = axios.create({
  baseURL: 'https://api.example.com',
  headers: {
    'Authorization': 'Basic ' + Buffer.from('username:password').toString('base64'),
  },
});

// Upload quiz
async function uploadQuiz(filePath) {
  const form = new FormData();
  form.append('file', fs.createReadStream(filePath));
  form.append('year', '2025');
  form.append('term', '2');
  form.append('week', '4');
  form.append('weekType', 'normal');

  const response = await client.post('/quiz/f2f/paperless-marking-worked-solutions', form, {
    headers: {
      ...form.getHeaders(),
      'X-Correlation-ID': uuidv4(),
    },
  });

  return response.data;
}
```

### Python with Requests
```python
import requests
import uuid
import base64

class TMSClient:
    def __init__(self, base_url, username, password):
        self.base_url = base_url
        credentials = base64.b64encode(f'{username}:{password}'.encode()).decode()
        self.headers = {
            'Authorization': f'Basic {credentials}',
        }
    
    def upload_quiz(self, file_path, year, term, week, week_type):
        headers = {
            **self.headers,
            'X-Correlation-ID': str(uuid.uuid4()),
        }
        
        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {
                'year': year,
                'term': term,
                'week': week,
                'weekType': week_type,
            }
            
            response = requests.post(
                f'{self.base_url}/quiz/f2f/paperless-marking-worked-solutions',
                headers=headers,
                files=files,
                data=data
            )
            
        return response.json()

# Usage
client = TMSClient('https://api.example.com', 'username', 'password')
result = client.upload_quiz('quiz.zip', 2025, 2, 4, 'normal')
```
