# Integration Guide

This guide provides step-by-step instructions for integrating with the TMS REST API.

## Integration Steps

### 1. Environment Setup

#### Development Environment
- **Base URL**: `http://localhost:3000`
- **Interactive Docs**: `http://localhost:3000/api/docs`
- **Credentials**: `your-dev-username:your-dev-password`

#### Production Environment
- **Base URL**: `https://api.tms.example.com`
- **Credentials**: Provided by system administrator

### 2. Authentication Setup

1. **Obtain Credentials**: Contact your system administrator
2. **Test Authentication**: Use the health endpoint to verify connectivity
3. **Implement Basic Auth**: Add authentication headers to all requests

### 3. Client Implementation

#### HTTP Client Configuration
```javascript
// Example configuration
const config = {
  baseURL: process.env.TMS_API_URL,
  timeout: 30000, // 30 seconds
  headers: {
    'Authorization': `Basic ${Buffer.from(`${username}:${password}`).toString('base64')}`,
    'User-Agent': 'MyApp/1.0.0',
  },
};
```

#### Error Handling
```javascript
try {
  const response = await client.get('/quiz/f2f/paperless-marking-worked-solutions', {
    headers: { 'X-Correlation-ID': generateUUID() },
    params: queryParams,
  });
  return response.data;
} catch (error) {
  if (error.response) {
    // API returned an error response
    console.error('API Error:', error.response.data);
    throw new APIError(error.response.data);
  } else if (error.request) {
    // Network error
    console.error('Network Error:', error.message);
    throw new NetworkError(error.message);
  } else {
    // Other error
    console.error('Error:', error.message);
    throw error;
  }
}
```

### 4. File Upload Implementation

#### Multipart Form Data
```javascript
const FormData = require('form-data');

async function uploadQuiz(filePath, metadata) {
  const form = new FormData();
  form.append('file', fs.createReadStream(filePath));

  // Add metadata fields
  Object.entries(metadata).forEach(([key, value]) => {
    form.append(key, value);
  });

  const response = await client.post('/quiz/f2f/paperless-marking-worked-solutions', form, {
    headers: {
      ...form.getHeaders(),
      'X-Correlation-ID': generateUUID(),
    },
    maxContentLength: 50 * 1024 * 1024, // 50MB limit
  });

  return response.data;
}
```

### 5. Response Processing

#### Success Response Handling
```javascript
function processQuizResponse(response) {
  const { message, data } = response;

  console.log('Success:', message);

  if (data.uploadedGifs) {
    console.log(`Uploaded ${data.gifCount} GIF files`);
    data.uploadedGifs.forEach(gif => {
      console.log(`- GIF ${gif.id}: ${gif.url}`);
    });
  }

  return data;
}
```

#### Error Response Handling
```javascript
function handleAPIError(error) {
  const { statusCode, message, details, correlationId } = error;

  console.error(`API Error [${correlationId}]: ${message}`);

  switch (statusCode) {
    case 400:
      // Validation error
      if (Array.isArray(details)) {
        details.forEach(detail => {
          console.error(`- ${detail.field}: ${Object.values(detail.constraints).join(', ')}`);
        });
      }
      break;
    case 401:
      // Authentication error
      console.error('Check your credentials');
      break;
    case 404:
      // Not found
      console.error('Resource not found');
      break;
    case 413:
      // File too large
      console.error('File size exceeds limit');
      break;
    default:
      console.error('Unexpected error');
  }
}
```

## Best Practices

### 1. Request Management

- **Correlation IDs**: Always include unique correlation IDs for request tracing
- **Timeouts**: Set appropriate timeouts (30-60 seconds for file uploads)
- **Retries**: Implement exponential backoff for transient failures
- **Rate Limiting**: Respect API rate limits (if applicable)

### 2. File Handling

- **Validation**: Validate file types and sizes before upload
- **Streaming**: Use streaming for large file uploads
- **Progress**: Implement upload progress tracking
- **Cleanup**: Clean up temporary files after processing

### 3. Security

- **HTTPS**: Always use HTTPS in production
- **Credential Storage**: Store credentials securely (environment variables, key vaults)
- **Logging**: Avoid logging sensitive information
- **Validation**: Validate all API responses

### 4. Monitoring

- **Logging**: Log all API interactions with correlation IDs
- **Metrics**: Track success rates, response times, and error rates
- **Alerting**: Set up alerts for API failures
- **Health Checks**: Regularly check API health

## Testing

### Unit Tests
```javascript
describe('TMS API Client', () => {
  it('should upload quiz successfully', async () => {
    const mockResponse = {
      message: 'Quiz uploaded successfully',
      data: { quizId: 123, gifCount: 2 }
    };

    nock('https://api.example.com')
      .post('/quiz/f2f/paperless-marking-worked-solutions')
      .reply(200, mockResponse);

    const result = await client.uploadQuiz('test.zip', metadata);
    expect(result.data.quizId).toBe(123);
  });
});
```

### Integration Tests
```javascript
describe('TMS API Integration', () => {
  it('should handle complete quiz workflow', async () => {
    // Upload quiz
    const uploadResult = await client.uploadQuiz('test.zip', metadata);
    const quizId = uploadResult.data.quizId;

    // Retrieve quiz
    const retrieveResult = await client.getQuiz(queryParams);
    expect(retrieveResult.data).toHaveLength(1);

    // Update quiz
    const updateResult = await client.updateQuiz(quizId, updateData);
    expect(updateResult.message).toContain('updated');

    // Delete quiz
    const deleteResult = await client.deleteQuiz(quizId);
    expect(deleteResult.message).toContain('deleted');
  });
});
```

## Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Check credentials encoding
   - Verify username/password
   - Ensure Authorization header format

2. **400 Bad Request**
   - Validate request parameters
   - Check required fields
   - Verify data types

3. **413 Payload Too Large**
   - Check file size (max 50MB)
   - Compress files if possible
   - Split large uploads

4. **500 Internal Server Error**
   - Check correlation ID in logs
   - Contact support with correlation ID
   - Retry after delay

### Debug Mode
```javascript
// Enable request/response logging
const client = axios.create({
  // ... config
});

client.interceptors.request.use(request => {
  console.log('Request:', request);
  return request;
});

client.interceptors.response.use(
  response => {
    console.log('Response:', response);
    return response;
  },
  error => {
    console.error('Error:', error.response || error);
    return Promise.reject(error);
  }
);
```

## Support

For integration support:
1. Check this documentation
2. Review API specification
3. Test with interactive documentation
4. Contact development team with correlation IDs
