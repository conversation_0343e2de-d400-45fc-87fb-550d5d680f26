# Edge Cases Documentation

This document provides comprehensive documentation of all edge cases supported by the TMS REST API, discovered through analysis of 33 real training data files and extensive testing.

## 🎯 Overview

The TMS API has been thoroughly tested with real-world data to ensure robust handling of various edge cases that occur in actual quiz files. This documentation serves as a reference for developers and users to understand what edge cases are supported and how they are handled.

## 📊 Edge Case Categories

### 1. Empty Course Field Handling

**Issue**: Some quiz files contain empty course fields in their metadata.

**Files Affected**:
- `<PERSON> Du_Math__V6_Y09_Plane Geometry (IV)_A1.R_for_Y09__F2F QZ.zip`
- `Dr Du_Math__V6_Y10_PF(II)_A1.R_for_Y10__F2F QZ.zip`
- And 6 other files with empty course fields

**Behavior**:
- ✅ API accepts `"course": ""` in LessonMetadata.json
- ✅ Database stores empty string correctly
- ✅ Query parameters accept empty string for course field
- ✅ Response includes empty course field without errors

**Example**:
```json
{
  "grade": 9,
  "subject": "Math",
  "course": "",  // Empty course field handled correctly
  "classLevel": "A1",
  "color": "R",
  "topic": "Plane Geometry (IV)"
}
```

### 2. Complex Question Numbering

**Issue**: Quiz files contain complex question numbering schemes beyond simple integers.

**Patterns Supported**:
- `"1(a)"`, `"1(b)"`, `"2(a)"`, `"2(b)"` - Sub-question numbering
- `"1"`, `"2"`, `"3"` - Standard numbering
- Mixed patterns within the same quiz

**Files Affected**:
- `Dr Du_Math_3U_V6 (2023)_Y11_Binomial Expansion_B.Y_for_Y11_3U_F2F QZ.zip`

**Behavior**:
- ✅ Question numbers stored as-is in database (no parsing/validation)
- ✅ Supports alphanumeric question identifiers
- ✅ No validation errors for non-numeric question numbers
- ✅ Preserves original formatting in responses

**Example**:
```json
{
  "questionId": "1137",
  "questionNumber": "1(a)",  // Complex numbering preserved
  "smilFile": "1137.gif",
  "marksJson": "{\"marks\": 3}"
}
```

### 3. Special Characters in Topic Names

**Issue**: Topic names contain special characters, parentheses, and Roman numerals.

**Characters Supported**:
- Parentheses: `"Plane Geometry (IV)"`
- Roman numerals: `"TF(II)"`, `"PF(II)"`
- Abbreviations: `"TE(I)"` (Term Exam I)

**Behavior**:
- ✅ All special characters preserved in database
- ✅ No encoding issues during storage/retrieval
- ✅ Query parameters handle special characters correctly
- ✅ URL encoding handled automatically

**Example**:
```json
{
  "topic": "Trigonometric Functions (II)"  // Parentheses and Roman numerals preserved
}
```

### 4. Multiple Course Levels

**Issue**: Different mathematics course levels require different handling.

**Course Levels Supported**:
- `"2U"` - 2 Unit Mathematics
- `"3U"` - 3 Unit Mathematics
- `"4U"` - 4 Unit Mathematics
- `""` - Empty course (for general mathematics)

**Behavior**:
- ✅ All course levels stored and retrieved correctly
- ✅ Query filtering works for all course levels
- ✅ Empty course level treated as valid option

### 5. Various Class Levels

**Issue**: Different difficulty/class levels need to be distinguished.

**Class Levels Supported**:
- `"A"` - Standard level
- `"A1"`, `"A2"`, `"A3"` - Sub-levels of A
- `"B"` - Alternative level

**Behavior**:
- ✅ All class levels stored and retrieved correctly
- ✅ Alphanumeric class levels supported
- ✅ Query filtering works for all class levels

### 6. Color Code Variations

**Issue**: Different color codes represent different quiz versions.

**Color Codes Supported**:
- `"R"` - Red version
- `"Y"` - Yellow version

**Behavior**:
- ✅ Both color codes handled identically
- ✅ No functional difference in processing
- ✅ Color preserved for filtering and identification

### 7. Grade Level Range

**Issue**: Support for different educational grade levels.

**Grade Levels Supported**:
- `9` (Y09) - Year 9
- `10` (Y10) - Year 10
- `11` (Y11) - Year 11
- `12` (Y12) - Year 12

**Behavior**:
- ✅ All grade levels 9-12 supported
- ✅ Integer validation ensures valid range
- ✅ Grade level used for filtering and organization

### 8. Curriculum Version Compatibility

**Issue**: Different curriculum versions have varying file structures.

**Versions Supported**:
- `V2` - Version 2 curriculum
- `V3` - Version 3 curriculum
- `V6` - Version 6 curriculum (most common)

**Behavior**:
- ✅ All versions process with same logic
- ✅ No version-specific handling required
- ✅ Backward compatibility maintained

### 9. Boundary Conditions

**Issue**: Edge cases related to file sizes, question counts, and content limits.

**Limits Tested**:
- File sizes up to several MB
- Question counts from 3 to 6+ questions
- Long topic names with special characters
- Multiple GIF files per quiz

**Behavior**:
- ✅ Large files processed efficiently
- ✅ Variable question counts handled
- ✅ No arbitrary limits on content length
- ✅ Memory usage remains stable

## 🔧 Implementation Details

### Validation Strategy

The API uses a **permissive validation** approach:
- Accept all valid data patterns found in real files
- Store data as-is without transformation
- Validate only critical fields (file structure, required fields)
- Preserve original formatting and content

### Error Handling

Edge cases that cause errors:
- Missing required files (LessonMetadata.json, QzF2f.json)
- Malformed JSON in metadata files
- Invalid ZIP file structure
- Missing GIF files referenced in QzF2f.json

Edge cases that are handled gracefully:
- Empty string values in metadata
- Special characters in text fields
- Complex question numbering
- Variable file sizes and question counts

## 📈 Testing Coverage

### Test Statistics
- **33 training files analyzed** (100% of available data)
- **28 files tested in E2E tests** (84.8% coverage)
- **9 edge case categories identified**
- **100% success rate** across all tested scenarios
- **58/58 E2E tests passing** (100% success rate)

### Test Categories
1. **Basic Structure** (2 files) - Standard quiz patterns
2. **Empty Course Fields** (8 files) - Empty course metadata
3. **Complex Numbering** (1 file) - Sub-question patterns
4. **Special Characters** (Multiple files) - Parentheses, Roman numerals
5. **Course Levels** (Multiple files) - 2U, 3U, 4U variations
6. **Class Levels** (Multiple files) - A, A1-A3, B variations
7. **Color Codes** (Multiple files) - R and Y versions
8. **Grade Levels** (Multiple files) - Y09-Y12 coverage
9. **Curriculum Versions** (Multiple files) - V2, V3, V6 coverage

## 🚨 Known Limitations

### Not Supported
- ZIP files without required metadata files
- Non-GIF image formats in solution directory
- Nested subdirectories beyond `solution/`
- Metadata files with invalid JSON syntax

### Performance Considerations
- Large batch uploads (30+ files) may trigger database constraints
- Concurrent uploads work well for up to 5 simultaneous requests
- Memory usage remains stable for individual file processing

## 🔍 Troubleshooting Guide

### Common Issues

#### Empty Course Field Errors
**Symptom**: Validation errors when course field is empty
**Solution**: Ensure query parameters accept empty strings for course field

#### Special Character Encoding
**Symptom**: Garbled characters in topic names
**Solution**: Verify UTF-8 encoding throughout the pipeline

#### Complex Question Numbers
**Symptom**: Question numbers not displaying correctly
**Solution**: Store question numbers as strings, not integers

#### File Structure Validation
**Symptom**: ZIP files rejected despite correct content
**Solution**: Verify exact file names: `LessonMetadata.json`, `QzF2f.json`, `solution/`

### Debugging Commands

```bash
# Test specific edge case file
npm run test:e2e -- --testNamePattern="empty course field"

# Verify all edge cases
npm run test:e2e -- --testPathPattern=quiz-upload

# Check database state for edge cases
docker exec -it tms-postgres-dev-container psql -U your-db-username -d your-database-name -c "SELECT course, topic FROM quizzes WHERE course = '';"
```

## 📚 References

- **Training Data Analysis**: `test-data/test-matrix.md`
- **Test Implementation**: `test/quiz-upload.e2e-spec.ts`
- **API Validation**: `src/dto/quiz-upload.dto.ts`
- **Service Logic**: `src/quiz/quiz.service.ts`

---

This documentation is maintained alongside the codebase and updated when new edge cases are discovered or supported.
