/**
 * Test Module Factory
 *
 * This utility provides functions for creating standardized test modules with database connections.
 * It helps ensure consistent test module configuration across all test files.
 *
 * The factory:
 * 1. Creates a test module with a real PostgreSQL database connection
 * 2. Configures the module with standard settings from .env
 * 3. Provides options for customizing the module configuration
 * 4. Follows our principle of "never mock components unless absolutely necessary"
 */

import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ModuleMetadata, INestApplication } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { Quiz, QuizAsset } from '../../src/entities';
import { MinioService } from '../../src/minio/minio.service';
import {
  cleanupDatabase,
  cleanupDatabaseData,
  cleanupMinIOBucket,
  cleanupTestData,
  cleanupDatabaseWithCallback,
} from './database-cleanup';
import minioConfig from '../../src/config/minio.config';

// Interface for database query result
interface DatabaseNameResult {
  current_database: string;
}

// Type assertion function for database query results
function assertDatabaseNameResult(result: unknown): DatabaseNameResult[] {
  return result as DatabaseNameResult[];
}

/**
 * Cleanup configuration options
 */
export interface CleanupOptions {
  /** Whether to enable automatic data cleanup (alias for enableDataCleanup) */
  withDataCleanup?: boolean;
  /** Whether to enable automatic data cleanup */
  enableDataCleanup?: boolean;
  /** Whether to include MinIO in cleanup operations */
  includeMinIO?: boolean;
  /** Whether to clean data before each test */
  cleanupBeforeEach?: boolean;
  /** Whether to clean data after each test */
  cleanupAfterEach?: boolean;
  /** Whether to clean data after all tests */
  cleanupAfterAll?: boolean;
  /** Custom cleanup function to run */
  customCleanup?: (
    dataSource: DataSource,
    minioService?: MinioService,
  ) => Promise<void>;
}

/**
 * Options for creating a test module
 */
export interface TestModuleOptions {
  /** Additional imports to include in the test module */
  imports?: ModuleMetadata['imports'];
  /** Controllers to include in the test module */
  controllers?: ModuleMetadata['controllers'];
  /** Providers to include in the test module */
  providers?: ModuleMetadata['providers'];
  /** Entities to include in TypeORM configuration */
  entities?: (new (...args: unknown[]) => unknown)[];
  /** Whether to make ConfigModule global */
  isGlobalConfig?: boolean;
  /** Whether to enable database logging */
  enableLogging?: boolean;
  /** Whether to enable database schema synchronization */
  enableSync?: boolean;
  /** Whether to drop the schema before synchronization */
  dropSchema?: boolean;
  /** Cleanup configuration options */
  cleanup?: CleanupOptions;
}

/**
 * Creates a test module with a database connection
 *
 * @param options Options for customizing the test module
 * @returns A promise that resolves to the compiled test module
 */
export async function createTestModule(
  options: TestModuleOptions = {},
): Promise<TestingModule> {
  // Set default options
  const {
    imports = [],
    controllers = [],
    providers = [],
    entities = [Quiz, QuizAsset],
    isGlobalConfig = false,
    enableLogging = false,
    enableSync = true,
    dropSchema = false,
    cleanup = {},
  } = options;

  // Set default cleanup options (support both withDataCleanup and enableDataCleanup)
  const {
    customCleanup,
    withDataCleanup = false,
    enableDataCleanup = false,
    includeMinIO = false,
  } = cleanup;

  // Use either withDataCleanup or enableDataCleanup (withDataCleanup takes precedence)
  // Note: These variables are used in the cleanup utilities and E2ETestApp function
  // customCleanup is used in the createTestApp function for cleanup utilities
  const dataCleanupEnabled = withDataCleanup || enableDataCleanup;
  // Suppress unused variable warning - these are used in the cleanup configuration
  void customCleanup;
  void dataCleanupEnabled;

  // Automatically include MinIO configuration and service if cleanup includes MinIO
  const enhancedImports = [...imports];
  const enhancedProviders = [...providers];

  if (includeMinIO) {
    // Add MinIO configuration
    enhancedImports.push(
      ConfigModule.forRoot({
        isGlobal: true,
        load: [minioConfig],
      }),
    );
    // Add MinIO service
    enhancedProviders.push(MinioService);
  }

  // Environment variables are now loaded from .env file

  // Create module metadata
  const moduleMetadata: ModuleMetadata = {
    imports: [
      ConfigModule.forRoot({
        isGlobal: isGlobalConfig,
        envFilePath: '.env',
      }),
      TypeOrmModule.forRootAsync({
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => {
          // For tests, use environment variables or test-specific defaults
          const username = configService.get('DB_USERNAME') || 'tms-test-user';
          const password =
            configService.get('DB_PASSWORD') || 'tms-test-password';
          const database =
            configService.get('DB_DATABASE') || 'tms-dev-postgres-database';

          return {
            type: 'postgres',
            host: configService.get('DB_HOST', 'localhost'),
            port: configService.get('DB_PORT', 5432),
            username,
            password,
            database,
            entities: entities,
            synchronize: configService.get('DB_SYNC', enableSync),
            logging: configService.get('DB_LOGGING', enableLogging),
            dropSchema: dropSchema,
          };
        },
      }),
      ...enhancedImports,
    ],
    controllers,
    providers: enhancedProviders,
  };

  // Create and compile the test module
  return Test.createTestingModule(moduleMetadata).compile();
}

/**
 * Result type for createTestApp with cleanup utilities
 */
export interface TestAppResult {
  moduleFixture: TestingModule;
  app: INestApplication;
  dataSource: DataSource;
  minioService?: MinioService;
  cleanup: {
    /** Clean database data only */
    cleanupData: () => Promise<void>;
    /** Clean MinIO bucket only (if MinIO is enabled) */
    cleanupMinIO: () => Promise<void>;
    /** Clean both database and MinIO data */
    cleanupAll: () => Promise<void>;
    /** Clean connections and close app */
    cleanupConnections: () => Promise<void>;
    /** Clean connections with callback support */
    cleanupConnectionsWithCallback: (done: () => void) => void;
  };
}

/**
 * Creates a test module with a database connection and initializes an application
 *
 * @param options Options for customizing the test module
 * @returns A promise that resolves to an object containing the module, app, dataSource, and cleanup utilities
 */
export async function createTestApp(
  options: TestModuleOptions = {},
): Promise<TestAppResult> {
  const moduleFixture = await createTestModule(options);
  const app = moduleFixture.createNestApplication();
  await app.init();

  // Get the database connection
  const dataSource = moduleFixture.get<DataSource>(DataSource);

  // Verify the database connection is using the correct database
  const dbName = assertDatabaseNameResult(
    await dataSource.query('SELECT current_database()'),
  );
  const dbResult: DatabaseNameResult = dbName[0];
  const expectedDatabase =
    process.env.DB_DATABASE || 'tms-dev-postgres-database';
  if (dbResult.current_database !== expectedDatabase) {
    throw new Error(
      `Connected to wrong database: ${dbResult.current_database}, expected: ${expectedDatabase}`,
    );
  }

  // Get MinIO service if cleanup includes MinIO
  const cleanup = options.cleanup || {};
  let minioService: MinioService | undefined;

  if (cleanup.includeMinIO) {
    minioService = moduleFixture.get<MinioService>(MinioService);
    minioService.onModuleInit();
  }

  // Create cleanup utilities
  const cleanupUtils = {
    cleanupData: async () => {
      if (cleanup.customCleanup) {
        await cleanup.customCleanup(dataSource, minioService);
      } else {
        await cleanupDatabaseData(dataSource);
      }
    },
    cleanupMinIO: async () => {
      if (minioService) {
        await cleanupMinIOBucket(minioService);
      }
    },
    cleanupAll: async () => {
      if (cleanup.customCleanup) {
        await cleanup.customCleanup(dataSource, minioService);
      } else {
        await cleanupTestData(dataSource, minioService);
      }
    },
    cleanupConnections: async () => {
      await cleanupDatabase(dataSource, app);
    },
    cleanupConnectionsWithCallback: (done: () => void) => {
      cleanupDatabaseWithCallback(dataSource, app, done);
    },
  };

  return {
    moduleFixture,
    app,
    dataSource,
    minioService,
    cleanup: cleanupUtils,
  };
}

/**
 * Result type for createE2ETestApp with optional cleanup utilities
 */
export interface E2ETestAppResult {
  app: INestApplication;
  dataSource: DataSource;
  minioService?: MinioService;
  cleanup?: {
    /** Clean database data only */
    cleanupData: () => Promise<void>;
    /** Clean MinIO bucket only (if MinIO is enabled) */
    cleanupMinIO: () => Promise<void>;
    /** Clean both database and MinIO data */
    cleanupAll: () => Promise<void>;
    /** Clean connections and close app */
    cleanupConnections: () => Promise<void>;
    /** Clean connections with callback support */
    cleanupConnectionsWithCallback: (done: () => void) => void;
  };
}

/**
 * Creates a test app for e2e tests
 *
 * This is a specialized version of createTestApp that returns the app and dataSource
 * in the format expected by e2e tests, with optional cleanup utilities.
 *
 * @param options Options for customizing the test module
 * @returns A promise that resolves to an object containing the app, dataSource, and optional cleanup utilities
 */
export async function createE2ETestApp(
  options: TestModuleOptions = {},
): Promise<E2ETestAppResult> {
  const result = await createTestApp(options);

  // Return cleanup utilities only if cleanup is configured
  const cleanup = options.cleanup;
  const dataCleanupEnabled =
    cleanup && (cleanup.withDataCleanup || cleanup.enableDataCleanup);
  if (dataCleanupEnabled) {
    return {
      app: result.app,
      dataSource: result.dataSource,
      minioService: result.minioService,
      cleanup: result.cleanup,
    };
  }

  // Backward compatibility: return only app and dataSource if no cleanup configured
  return {
    app: result.app,
    dataSource: result.dataSource,
  };
}

/**
 * Helper function to create common cleanup patterns
 */
export function createCleanupHooks(
  testResult: TestAppResult | E2ETestAppResult,
  options: CleanupOptions = {},
) {
  const {
    cleanupBeforeEach = false,
    cleanupAfterEach = false,
    cleanupAfterAll = true,
  } = options;

  const hooks: {
    beforeEach?: () => Promise<void>;
    afterEach?: () => Promise<void>;
    afterAll?: () => Promise<void> | void;
  } = {};

  if ('cleanup' in testResult && testResult.cleanup) {
    if (cleanupBeforeEach) {
      hooks.beforeEach = testResult.cleanup.cleanupAll;
    }

    if (cleanupAfterEach) {
      hooks.afterEach = testResult.cleanup.cleanupAll;
    }

    if (cleanupAfterAll) {
      hooks.afterAll = testResult.cleanup.cleanupConnections;
    }
  } else {
    // Fallback to traditional cleanup for backward compatibility
    if (cleanupAfterAll) {
      hooks.afterAll = () => {
        return new Promise<void>((resolve) => {
          cleanupDatabaseWithCallback(
            testResult.dataSource,
            testResult.app,
            resolve,
          );
        });
      };
    }
  }

  return hooks;
}
