/**
 * Health Endpoint E2E Tests
 *
 * These tests use a real PostgreSQL database connection instead of mocks,
 * following our principle of "never mock components unless absolutely necessary".
 *
 * The tests connect to a dedicated test database instance (configured in .env)
 * to provide more realistic testing of database interactions.
 */

import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AppModule } from '../src/app.module';
import { DataSource } from 'typeorm';
import { cleanupDatabaseWithCallback } from './utils/database-cleanup';
import { createE2ETestApp } from './utils/test-module-factory';
import { Server } from 'http';

// Helper function to get typed HTTP server for supertest
// Since we're using Express (NestJS default), we can safely cast to Node.js Server
function getHttpServer(app: INestApplication) {
  return request(app.getHttpServer() as Server);
}

// Interface for health check response
interface HealthResponse {
  status: string;
  timestamp: string;
  service: string;
  version: string;
  components: {
    database: {
      status: string;
      message: string;
    };
  };
}

describe('Health Endpoint (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;

  // Note: We're not using fake timers in E2E tests as they can interfere with
  // real database connections and network requests, causing timeouts

  // Increase timeout for beforeAll to 60 seconds
  beforeAll(async () => {
    // Create a test app with a real database connection using the shared factory
    const testApp = await createE2ETestApp({
      imports: [AppModule],
      isGlobalConfig: true,
    });

    app = testApp.app;
    dataSource = testApp.dataSource;
  }, 60000); // 60 second timeout

  // Use the shared cleanup utility with increased timeout
  afterAll((done) => {
    cleanupDatabaseWithCallback(dataSource, app, done);
  }, 60000); // 60 second timeout

  it('/health (GET) should return health status with database information', () => {
    return getHttpServer(app)
      .get('/health')
      .set('X-Correlation-ID', '123e4567-e89b-12d3-a456-************')
      .expect(200)
      .expect((res: { body: HealthResponse }) => {
        // Check basic health properties
        expect(res.body).toHaveProperty('status');
        expect(res.body).toHaveProperty('timestamp');
        expect(res.body).toHaveProperty('service');
        expect(res.body).toHaveProperty('version');
        expect(res.body.status).toBe('ok');
        expect(res.body.service).toBe('tms-api');

        // Check database status
        expect(res.body).toHaveProperty('components');
        expect(res.body.components).toHaveProperty('database');
        expect(res.body.components.database).toHaveProperty('status');
        expect(res.body.components.database).toHaveProperty('message');
        expect(res.body.components.database.status).toBe('ok');
        expect(res.body.components.database.message).toBe(
          'Database connection is healthy',
        );
      });
  });

  it('should return the correct content type', () => {
    return getHttpServer(app)
      .get('/health')
      .set('X-Correlation-ID', '123e4567-e89b-12d3-a456-************')
      .expect('Content-Type', /json/);
  });

  it('should respond quickly (under 500ms)', async () => {
    const start = Date.now();
    await getHttpServer(app)
      .get('/health')
      .set('X-Correlation-ID', '123e4567-e89b-12d3-a456-************');
    const duration = Date.now() - start;

    // Increased timeout to account for database connection
    expect(duration).toBeLessThan(500);
  });
});
