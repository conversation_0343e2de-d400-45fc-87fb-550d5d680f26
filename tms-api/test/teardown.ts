/**
 * Global teardown for Jest
 *
 * This file is used to clean up any resources that might be left open after tests
 * have completed. It helps prevent the "worker process has failed to exit gracefully"
 * warning.
 */

import { DataSource } from 'typeorm';

interface NodeTimer {
  unref?: () => void;
}

interface NodeError extends Error {
  message: string;
}

interface GlobalWithTypeORM {
  __TYPEORM_DATA_SOURCES__?: DataSource[];
  gc?: () => void;
}

export default async (): Promise<void> => {
  console.log('Running global teardown...');

  // Add a delay to allow any pending operations to complete
  await new Promise<void>((resolve) => {
    const timer = setTimeout(resolve, 1000) as NodeTimer;
    if (timer.unref) {
      timer.unref(); // Ensure this timer doesn't prevent process exit
    }
  });

  // Attempt to close any remaining TypeORM connections
  try {
    // Try to access global DataSource instances if they exist
    const globalWithTypeORM = global as GlobalWithTypeORM;
    const dataSources = globalWithTypeORM.__TYPEORM_DATA_SOURCES__ || [];
    if (dataSources.length > 0) {
      console.log(
        `Closing ${dataSources.length} active DataSource instances...`,
      );
      await Promise.all(
        dataSources.map(async (dataSource: DataSource) => {
          if (dataSource && dataSource.isInitialized) {
            try {
              await dataSource.destroy();
              console.log(`Closed DataSource connection`);
            } catch (err) {
              const error = err as NodeError;
              console.error(`Error closing DataSource:`, error.message);
            }
          }
        }),
      );
    }
  } catch {
    // TypeORM not available or no connections to close
    console.log('No TypeORM connections to close');
  }

  // Force garbage collection if available (Node.js with --expose-gc flag)
  const globalWithGC = global as GlobalWithTypeORM;
  if (globalWithGC.gc) {
    globalWithGC.gc();
  }

  // Final delay to ensure all resources are released
  await new Promise<void>((resolve) => {
    const timer = setTimeout(resolve, 500) as NodeTimer;
    if (timer.unref) {
      timer.unref(); // Ensure this timer doesn't prevent process exit
    }
  });

  console.log('Global teardown completed');
};
