/**
 * Database Test Utilities
 *
 * This file provides utility functions for testing with a real PostgreSQL database.
 * It follows our principle of "never mock components unless absolutely necessary"
 * by providing tools to work with an actual test database instance.
 */

import { DataSource } from 'typeorm';

/**
 * Clear all data from the specified tables
 * @param dataSource The TypeORM data source
 * @param tables An array of table names to clear
 */
export async function clearTables(
  dataSource: DataSource,
  tables: string[],
): Promise<void> {
  for (const table of tables) {
    await dataSource.query(`TRUNCATE TABLE "${table}" CASCADE`);
  }
}

/**
 * Interface for table query result
 */
interface TableResult {
  tablename: string;
}

/**
 * Reset the database to a clean state
 * @param dataSource The TypeORM data source
 */
export async function resetDatabase(dataSource: DataSource): Promise<void> {
  // Get all table names in the public schema
  const tables: TableResult[] = await dataSource.query(`
    SELECT tablename FROM pg_tables WHERE schemaname = 'public'
  `);

  // Clear all tables
  await clearTables(
    dataSource,
    tables.map((table: TableResult) => table.tablename),
  );
}

/**
 * Initialize the database with test data
 * @param dataSource The TypeORM data source
 */
export function initializeTestData(dataSource: DataSource): void {
  // This function can be expanded to load specific test data as needed
  console.log('Initializing test data for:', dataSource.options.database);

  // Example: Create a test user (when needed)
  // await dataSource.query(`
  //   INSERT INTO users (name, email) VALUES ('Test User', '<EMAIL>')
  // `);
}

/**
 * Run a function with a clean database
 * @param dataSource The TypeORM data source
 * @param fn The function to run with a clean database
 */
export async function withCleanDatabase<T>(
  dataSource: DataSource,
  fn: () => Promise<T>,
): Promise<T> {
  try {
    await resetDatabase(dataSource);
    return await fn();
  } finally {
    await resetDatabase(dataSource);
  }
}
